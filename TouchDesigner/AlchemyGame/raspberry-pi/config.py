#!/usr/bin/env python3
"""
Čarodejnícka Alchymistická Hra - Konfigurácia
Centrálne nastavenia pre Raspberry Pi kontrolér
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class GPIOPins:
    """GPIO pin konfigurácia"""
    # NFC čítačka RC522
    NFC_RST_PIN: int = 11
    NFC_IRQ_PIN: int = 24
    
    # LED pásik WS2812B
    LED_PIN: int = 18
    
    # Ml<PERSON><PERSON> tryska (relé)
    FOG_RELAY_PIN: int = 13
    
    # Status LEDs
    RED_LED_PIN: int = 16
    GREEN_LED_PIN: int = 19
    
    # Rezervné piny
    SPARE_PIN_1: int = 20
    SPARE_PIN_2: int = 21


@dataclass
class SensorConfig:
    """Konfigur<PERSON>cia senzorov"""
    # Váha
    WEIGHT_CALIBRATION_SAMPLES: int = 10
    WEIGHT_READING_INTERVAL: float = 0.5
    WEIGHT_STABILITY_THRESHOLD: float = 0.1
    WEIGHT_TIMEOUT: float = 30.0
    
    # NFC
    NFC_READING_INTERVAL: float = 0.2
    NFC_TIMEOUT: float = 5.0
    
    # Akcelerometer
    ACCEL_READING_INTERVAL: float = 0.1
    ACCEL_CALIBRATION_SAMPLES: int = 20
    MIXING_THRESHOLD: float = 1.5
    ROTATION_DETECTION_INTERVAL: float = 0.5


@dataclass
class LEDConfig:
    """Konfigurácia LED efektov"""
    NUM_LEDS: int = 30
    BRIGHTNESS: float = 0.8
    FPS: int = 20
    
    # Efekty
    STARTUP_DURATION: float = 3.0
    SHUTDOWN_DURATION: float = 2.0
    SUCCESS_DURATION: float = 2.0
    ERROR_DURATION: float = 1.0
    MIXING_SPEED_MULTIPLIER: Dict[str, float] = None
    
    def __post_init__(self):
        if self.MIXING_SPEED_MULTIPLIER is None:
            self.MIXING_SPEED_MULTIPLIER = {
                'slow': 0.5,
                'medium': 1.0,
                'fast': 2.0
            }


@dataclass
class BluetoothConfig:
    """Konfigurácia Bluetooth"""
    SERVICE_UUID: str = "12345678-1234-5678-9012-123456789abc"
    CHARACTERISTIC_UUID: str = "*************-8765-2109-cba987654321"
    DEVICE_NAME: str = "AlchemyPi"
    ADVERTISING_INTERVAL: float = 1.0
    CONNECTION_TIMEOUT: float = 30.0
    MESSAGE_TIMEOUT: float = 5.0


@dataclass
class GameConfig:
    """Konfigurácia hry"""
    # Časové limity
    DEFAULT_GAME_TIME: int = 900  # 15 minút
    STEP_TIMEOUT: float = 120.0   # 2 minúty na krok
    
    # Tolerancie
    WEIGHT_TOLERANCE_MULTIPLIER: float = 1.0
    MIXING_TOLERANCE: int = 1  # ±1 rotácia
    
    # Skórovanie
    BASE_STEP_POINTS: int = 50
    TIME_BONUS_MAX: int = 30
    PERFECT_BONUS: int = 100
    ERROR_PENALTY: int = 10
    
    # Efekty
    FOG_DURATION_MULTIPLIER: float = 1.0
    LED_EFFECT_INTENSITY: float = 1.0


class Config:
    """Hlavná konfiguračná trieda"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "/etc/alchemy_game/config.json"
        
        # Inicializácia s predvolenými hodnotami
        self.gpio = GPIOPins()
        self.sensors = SensorConfig()
        self.leds = LEDConfig()
        self.bluetooth = BluetoothConfig()
        self.game = GameConfig()
        
        # Načítanie konfigurácie zo súboru
        self.load_config()
        
        # Validácia konfigurácie
        self.validate_config()
        
        logger.info("⚙️ Konfigurácia načítaná")

    def load_config(self):
        """Načítanie konfigurácie zo súboru"""
        try:
            if os.path.exists(self.config_file):
                logger.info(f"📂 Načítavam konfiguráciu z: {self.config_file}")
                
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                self.apply_config_data(config_data)
                logger.info("✅ Konfigurácia načítaná zo súboru")
            else:
                logger.info("📂 Konfiguračný súbor neexistuje, používam predvolené hodnoty")
                self.save_config()  # Uloženie predvolených hodnôt
                
        except Exception as e:
            logger.error(f"❌ Chyba pri načítaní konfigurácie: {e}")
            logger.info("📂 Používam predvolené hodnoty")

    def apply_config_data(self, config_data: Dict[str, Any]):
        """Aplikácia konfiguračných dát"""
        try:
            # GPIO konfigurácia
            if 'gpio' in config_data:
                gpio_data = config_data['gpio']
                for key, value in gpio_data.items():
                    if hasattr(self.gpio, key):
                        setattr(self.gpio, key, value)
            
            # Sensor konfigurácia
            if 'sensors' in config_data:
                sensor_data = config_data['sensors']
                for key, value in sensor_data.items():
                    if hasattr(self.sensors, key):
                        setattr(self.sensors, key, value)
            
            # LED konfigurácia
            if 'leds' in config_data:
                led_data = config_data['leds']
                for key, value in led_data.items():
                    if hasattr(self.leds, key):
                        setattr(self.leds, key, value)
            
            # Bluetooth konfigurácia
            if 'bluetooth' in config_data:
                bt_data = config_data['bluetooth']
                for key, value in bt_data.items():
                    if hasattr(self.bluetooth, key):
                        setattr(self.bluetooth, key, value)
            
            # Game konfigurácia
            if 'game' in config_data:
                game_data = config_data['game']
                for key, value in game_data.items():
                    if hasattr(self.game, key):
                        setattr(self.game, key, value)
                        
        except Exception as e:
            logger.error(f"❌ Chyba pri aplikácii konfigurácie: {e}")

    def save_config(self):
        """Uloženie konfigurácie do súboru"""
        try:
            # Vytvorenie adresára ak neexistuje
            config_dir = os.path.dirname(self.config_file)
            if config_dir and not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            
            # Konverzia na slovník
            config_data = {
                'gpio': {
                    'NFC_RST_PIN': self.gpio.NFC_RST_PIN,
                    'NFC_IRQ_PIN': self.gpio.NFC_IRQ_PIN,
                    'LED_PIN': self.gpio.LED_PIN,
                    'FOG_RELAY_PIN': self.gpio.FOG_RELAY_PIN,
                    'RED_LED_PIN': self.gpio.RED_LED_PIN,
                    'GREEN_LED_PIN': self.gpio.GREEN_LED_PIN,
                    'SPARE_PIN_1': self.gpio.SPARE_PIN_1,
                    'SPARE_PIN_2': self.gpio.SPARE_PIN_2
                },
                'sensors': {
                    'WEIGHT_CALIBRATION_SAMPLES': self.sensors.WEIGHT_CALIBRATION_SAMPLES,
                    'WEIGHT_READING_INTERVAL': self.sensors.WEIGHT_READING_INTERVAL,
                    'WEIGHT_STABILITY_THRESHOLD': self.sensors.WEIGHT_STABILITY_THRESHOLD,
                    'WEIGHT_TIMEOUT': self.sensors.WEIGHT_TIMEOUT,
                    'NFC_READING_INTERVAL': self.sensors.NFC_READING_INTERVAL,
                    'NFC_TIMEOUT': self.sensors.NFC_TIMEOUT,
                    'ACCEL_READING_INTERVAL': self.sensors.ACCEL_READING_INTERVAL,
                    'ACCEL_CALIBRATION_SAMPLES': self.sensors.ACCEL_CALIBRATION_SAMPLES,
                    'MIXING_THRESHOLD': self.sensors.MIXING_THRESHOLD,
                    'ROTATION_DETECTION_INTERVAL': self.sensors.ROTATION_DETECTION_INTERVAL
                },
                'leds': {
                    'NUM_LEDS': self.leds.NUM_LEDS,
                    'BRIGHTNESS': self.leds.BRIGHTNESS,
                    'FPS': self.leds.FPS,
                    'STARTUP_DURATION': self.leds.STARTUP_DURATION,
                    'SHUTDOWN_DURATION': self.leds.SHUTDOWN_DURATION,
                    'SUCCESS_DURATION': self.leds.SUCCESS_DURATION,
                    'ERROR_DURATION': self.leds.ERROR_DURATION,
                    'MIXING_SPEED_MULTIPLIER': self.leds.MIXING_SPEED_MULTIPLIER
                },
                'bluetooth': {
                    'SERVICE_UUID': self.bluetooth.SERVICE_UUID,
                    'CHARACTERISTIC_UUID': self.bluetooth.CHARACTERISTIC_UUID,
                    'DEVICE_NAME': self.bluetooth.DEVICE_NAME,
                    'ADVERTISING_INTERVAL': self.bluetooth.ADVERTISING_INTERVAL,
                    'CONNECTION_TIMEOUT': self.bluetooth.CONNECTION_TIMEOUT,
                    'MESSAGE_TIMEOUT': self.bluetooth.MESSAGE_TIMEOUT
                },
                'game': {
                    'DEFAULT_GAME_TIME': self.game.DEFAULT_GAME_TIME,
                    'STEP_TIMEOUT': self.game.STEP_TIMEOUT,
                    'WEIGHT_TOLERANCE_MULTIPLIER': self.game.WEIGHT_TOLERANCE_MULTIPLIER,
                    'MIXING_TOLERANCE': self.game.MIXING_TOLERANCE,
                    'BASE_STEP_POINTS': self.game.BASE_STEP_POINTS,
                    'TIME_BONUS_MAX': self.game.TIME_BONUS_MAX,
                    'PERFECT_BONUS': self.game.PERFECT_BONUS,
                    'ERROR_PENALTY': self.game.ERROR_PENALTY,
                    'FOG_DURATION_MULTIPLIER': self.game.FOG_DURATION_MULTIPLIER,
                    'LED_EFFECT_INTENSITY': self.game.LED_EFFECT_INTENSITY
                }
            }
            
            # Uloženie do súboru
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ Konfigurácia uložená do: {self.config_file}")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri ukladaní konfigurácie: {e}")

    def validate_config(self):
        """Validácia konfigurácie"""
        try:
            logger.info("🔍 Validujem konfiguráciu...")
            
            # Validácia GPIO pinov
            self.validate_gpio_pins()
            
            # Validácia senzorov
            self.validate_sensor_config()
            
            # Validácia LED konfigurácie
            self.validate_led_config()
            
            # Validácia Bluetooth konfigurácie
            self.validate_bluetooth_config()
            
            # Validácia hernej konfigurácie
            self.validate_game_config()
            
            logger.info("✅ Konfigurácia je platná")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri validácii konfigurácie: {e}")
            raise

    def validate_gpio_pins(self):
        """Validácia GPIO pinov"""
        valid_pins = list(range(2, 28))  # GPIO 2-27
        
        pins_to_check = [
            ('NFC_RST_PIN', self.gpio.NFC_RST_PIN),
            ('NFC_IRQ_PIN', self.gpio.NFC_IRQ_PIN),
            ('LED_PIN', self.gpio.LED_PIN),
            ('FOG_RELAY_PIN', self.gpio.FOG_RELAY_PIN),
            ('RED_LED_PIN', self.gpio.RED_LED_PIN),
            ('GREEN_LED_PIN', self.gpio.GREEN_LED_PIN)
        ]
        
        used_pins = set()
        
        for pin_name, pin_number in pins_to_check:
            if pin_number not in valid_pins:
                raise ValueError(f"Neplatný GPIO pin {pin_name}: {pin_number}")
            
            if pin_number in used_pins:
                raise ValueError(f"GPIO pin {pin_number} je použitý viackrát")
            
            used_pins.add(pin_number)

    def validate_sensor_config(self):
        """Validácia sensor konfigurácie"""
        if self.sensors.WEIGHT_READING_INTERVAL <= 0:
            raise ValueError("WEIGHT_READING_INTERVAL musí byť kladné číslo")
        
        if self.sensors.NFC_READING_INTERVAL <= 0:
            raise ValueError("NFC_READING_INTERVAL musí byť kladné číslo")
        
        if self.sensors.ACCEL_READING_INTERVAL <= 0:
            raise ValueError("ACCEL_READING_INTERVAL musí byť kladné číslo")

    def validate_led_config(self):
        """Validácia LED konfigurácie"""
        if self.leds.NUM_LEDS <= 0:
            raise ValueError("NUM_LEDS musí byť kladné číslo")
        
        if not 0 <= self.leds.BRIGHTNESS <= 1:
            raise ValueError("BRIGHTNESS musí byť medzi 0 a 1")
        
        if self.leds.FPS <= 0:
            raise ValueError("FPS musí byť kladné číslo")

    def validate_bluetooth_config(self):
        """Validácia Bluetooth konfigurácie"""
        if len(self.bluetooth.SERVICE_UUID) != 36:
            raise ValueError("SERVICE_UUID musí mať 36 znakov")
        
        if len(self.bluetooth.CHARACTERISTIC_UUID) != 36:
            raise ValueError("CHARACTERISTIC_UUID musí mať 36 znakov")
        
        if not self.bluetooth.DEVICE_NAME:
            raise ValueError("DEVICE_NAME nesmie byť prázdny")

    def validate_game_config(self):
        """Validácia hernej konfigurácie"""
        if self.game.DEFAULT_GAME_TIME <= 0:
            raise ValueError("DEFAULT_GAME_TIME musí byť kladné číslo")
        
        if self.game.STEP_TIMEOUT <= 0:
            raise ValueError("STEP_TIMEOUT musí byť kladné číslo")

    def get_config_summary(self) -> Dict[str, Any]:
        """Získanie súhrnu konfigurácie"""
        return {
            'config_file': self.config_file,
            'gpio_pins': {
                'nfc_rst': self.gpio.NFC_RST_PIN,
                'led': self.gpio.LED_PIN,
                'fog_relay': self.gpio.FOG_RELAY_PIN
            },
            'led_count': self.leds.NUM_LEDS,
            'bluetooth_device': self.bluetooth.DEVICE_NAME,
            'game_time': self.game.DEFAULT_GAME_TIME
        }

    # Convenience properties pre rýchly prístup
    @property
    def NFC_RST_PIN(self) -> int:
        return self.gpio.NFC_RST_PIN
    
    @property
    def NFC_IRQ_PIN(self) -> int:
        return self.gpio.NFC_IRQ_PIN
    
    @property
    def LED_PIN(self) -> int:
        return self.gpio.LED_PIN
    
    @property
    def FOG_RELAY_PIN(self) -> int:
        return self.gpio.FOG_RELAY_PIN
    
    @property
    def NUM_LEDS(self) -> int:
        return self.leds.NUM_LEDS
    
    @property
    def SERVICE_UUID(self) -> str:
        return self.bluetooth.SERVICE_UUID
    
    @property
    def CHARACTERISTIC_UUID(self) -> str:
        return self.bluetooth.CHARACTERISTIC_UUID
