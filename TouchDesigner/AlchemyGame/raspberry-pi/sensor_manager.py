#!/usr/bin/env python3
"""
Čarodejnícka Alchymistická Hra - Sensor Manager
<PERSON>pra<PERSON><PERSON> všetky senzory pripojené k Raspberry Pi
"""

import asyncio
import logging
import time
import random
from typing import Optional, Callable, Dict, Any
from dataclasses import dataclass

try:
    import RPi.GPIO as GPIO
    import spidev
    import smbus
    GPIO_AVAILABLE = True
except ImportError:
    GPIO_AVAILABLE = False
    logging.warning("⚠️ GPIO knižnice nie sú dostupné - senzory budú simulované")

logger = logging.getLogger(__name__)


@dataclass
class SensorReading:
    """Štruktúra čítania senzora"""
    sensor_type: str
    value: Any
    timestamp: float
    unit: Optional[str] = None
    raw_value: Optional[Any] = None


class SensorManager:
    """Manager pre všetky senzory"""
    
    def __init__(self, config):
        self.config = config
        self.is_monitoring = False
        self.simulation_mode = not GPIO_AVAILABLE
        
        # Callback funkcie
        self.weight_callback: Optional[Callable] = None
        self.nfc_callback: Optional[Callable] = None
        self.accelerometer_callback: Optional[Callable] = None
        
        # Senzory
        self.nfc_reader = None
        self.accelerometer = None
        self.bluetooth_scale = None
        
        # Simulačné dáta
        self.simulated_weight = 0.0
        self.simulated_nfc_present = False
        self.simulated_mixing = False
        
        # Kalibračné dáta
        self.weight_calibration_offset = 0.0
        self.accelerometer_calibration = {'x': 0, 'y': 0, 'z': 0}
        
        logger.info("📡 Sensor Manager inicializovaný")

    async def initialize(self):
        """Inicializácia všetkých senzorov"""
        try:
            logger.info("📡 Inicializujem senzory...")
            
            if self.simulation_mode:
                logger.info("📡 Senzory v simulačnom režime")
                return
            
            # Inicializácia GPIO
            GPIO.setmode(GPIO.BCM)
            GPIO.setwarnings(False)
            
            # Inicializácia NFC čítačky
            await self.initialize_nfc_reader()
            
            # Inicializácia akcelerometra
            await self.initialize_accelerometer()
            
            # Inicializácia Bluetooth váhy
            await self.initialize_bluetooth_scale()
            
            # Kalibrácia senzorov
            await self.calibrate_sensors()
            
            logger.info("✅ Všetky senzory inicializované")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri inicializácii senzorov: {e}")
            self.simulation_mode = True
            logger.info("📡 Prepínam na simulačný režim")

    async def initialize_nfc_reader(self):
        """Inicializácia NFC čítačky RC522"""
        try:
            logger.info("📡 Inicializujem NFC čítačku...")
            
            # Nastavenie SPI pre RC522
            self.nfc_reader = {
                'spi': spidev.SpiDev(),
                'pin_rst': self.config.NFC_RST_PIN,
                'pin_irq': self.config.NFC_IRQ_PIN
            }
            
            # Otvorenie SPI
            self.nfc_reader['spi'].open(0, 0)  # Bus 0, Device 0
            self.nfc_reader['spi'].max_speed_hz = 1000000
            
            # Nastavenie GPIO pinov
            GPIO.setup(self.nfc_reader['pin_rst'], GPIO.OUT)
            GPIO.setup(self.nfc_reader['pin_irq'], GPIO.IN, pull_up_down=GPIO.PUD_UP)
            
            # Reset NFC čítačky
            GPIO.output(self.nfc_reader['pin_rst'], GPIO.LOW)
            await asyncio.sleep(0.1)
            GPIO.output(self.nfc_reader['pin_rst'], GPIO.HIGH)
            await asyncio.sleep(0.1)
            
            logger.info("✅ NFC čítačka inicializovaná")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri inicializácii NFC čítačky: {e}")
            raise

    async def initialize_accelerometer(self):
        """Inicializácia akcelerometra MPU6050"""
        try:
            logger.info("📡 Inicializujem akcelerometer...")
            
            # Inicializácia I2C
            self.accelerometer = {
                'bus': smbus.SMBus(1),  # I2C bus 1
                'address': 0x68,        # MPU6050 adresa
                'last_reading': {'x': 0, 'y': 0, 'z': 0},
                'rotation_count': 0,
                'last_rotation_time': 0
            }
            
            # Prebudenie MPU6050
            self.accelerometer['bus'].write_byte_data(
                self.accelerometer['address'], 0x6B, 0
            )
            
            logger.info("✅ Akcelerometer inicializovaný")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri inicializácii akcelerometra: {e}")
            raise

    async def initialize_bluetooth_scale(self):
        """Inicializácia Bluetooth váhy"""
        try:
            logger.info("📡 Inicializujem Bluetooth váhu...")
            
            # Simulácia Bluetooth váhy
            self.bluetooth_scale = {
                'connected': False,
                'last_weight': 0.0,
                'stable': True
            }
            
            # V reálnej implementácii by sa tu pripojilo k Bluetooth váhe
            
            logger.info("✅ Bluetooth váha inicializovaná")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri inicializácii Bluetooth váhy: {e}")
            raise

    async def calibrate_sensors(self):
        """Kalibrácia všetkých senzorov"""
        try:
            logger.info("⚖️ Kalibrácia senzorov...")
            
            if self.simulation_mode:
                logger.info("⚖️ Kalibrácia v simulačnom režime")
                return
            
            # Kalibrácia váhy (bez záťaže)
            await self.calibrate_weight()
            
            # Kalibrácia akcelerometra (v pokoji)
            await self.calibrate_accelerometer()
            
            logger.info("✅ Kalibrácia dokončená")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri kalibrácii: {e}")

    async def calibrate_weight(self):
        """Kalibrácia váhy"""
        try:
            logger.info("⚖️ Kalibrácia váhy...")
            
            # Načítanie niekoľkých vzoriek bez záťaže
            samples = []
            for _ in range(10):
                weight = await self.read_raw_weight()
                samples.append(weight)
                await asyncio.sleep(0.1)
            
            # Výpočet offsetu
            self.weight_calibration_offset = sum(samples) / len(samples)
            
            logger.info(f"✅ Váha kalibrovaná, offset: {self.weight_calibration_offset:.2f}")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri kalibrácii váhy: {e}")

    async def calibrate_accelerometer(self):
        """Kalibrácia akcelerometra"""
        try:
            logger.info("📐 Kalibrácia akcelerometra...")
            
            # Načítanie niekoľkých vzoriek v pokoji
            samples = {'x': [], 'y': [], 'z': []}
            for _ in range(20):
                reading = await self.read_raw_accelerometer()
                samples['x'].append(reading['x'])
                samples['y'].append(reading['y'])
                samples['z'].append(reading['z'])
                await asyncio.sleep(0.05)
            
            # Výpočet kalibračných hodnôt
            self.accelerometer_calibration = {
                'x': sum(samples['x']) / len(samples['x']),
                'y': sum(samples['y']) / len(samples['y']),
                'z': sum(samples['z']) / len(samples['z']) - 16384  # Gravitácia
            }
            
            logger.info("✅ Akcelerometer kalibrovaný")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri kalibrácii akcelerometra: {e}")

    async def start_monitoring(self):
        """Spustenie monitorovania senzorov"""
        try:
            logger.info("🔄 Spúšťam monitorovanie senzorov...")
            
            self.is_monitoring = True
            
            # Spustenie monitorovacích slučiek
            tasks = [
                asyncio.create_task(self.monitor_weight()),
                asyncio.create_task(self.monitor_nfc()),
                asyncio.create_task(self.monitor_accelerometer())
            ]
            
            await asyncio.gather(*tasks)
            
        except Exception as e:
            logger.error(f"❌ Chyba pri monitorovaní senzorov: {e}")

    async def monitor_weight(self):
        """Monitorovanie váhy"""
        logger.info("⚖️ Monitorovanie váhy spustené")
        
        while self.is_monitoring:
            try:
                weight = await self.read_weight()
                
                # Callback ak sa hmotnosť zmenila
                if self.weight_callback and abs(weight - getattr(self, 'last_weight', 0)) > 0.1:
                    await self.weight_callback(SensorReading(
                        sensor_type='weight',
                        value=weight,
                        timestamp=time.time(),
                        unit='g'
                    ))
                    self.last_weight = weight
                
                await asyncio.sleep(0.5)  # Čítanie každých 500ms
                
            except Exception as e:
                logger.error(f"❌ Chyba pri čítaní váhy: {e}")
                await asyncio.sleep(1)

    async def monitor_nfc(self):
        """Monitorovanie NFC čítačky"""
        logger.info("📡 Monitorovanie NFC spustené")
        
        while self.is_monitoring:
            try:
                nfc_data = await self.read_nfc()
                
                if nfc_data and self.nfc_callback:
                    await self.nfc_callback(SensorReading(
                        sensor_type='nfc',
                        value=nfc_data,
                        timestamp=time.time()
                    ))
                
                await asyncio.sleep(0.2)  # Čítanie každých 200ms
                
            except Exception as e:
                logger.error(f"❌ Chyba pri čítaní NFC: {e}")
                await asyncio.sleep(1)

    async def monitor_accelerometer(self):
        """Monitorovanie akcelerometra"""
        logger.info("📐 Monitorovanie akcelerometra spustené")
        
        while self.is_monitoring:
            try:
                accel_data = await self.read_accelerometer()
                
                # Detekcia miešania
                mixing_detected = self.detect_mixing(accel_data)
                
                if mixing_detected and self.accelerometer_callback:
                    await self.accelerometer_callback(SensorReading(
                        sensor_type='accelerometer',
                        value=accel_data,
                        timestamp=time.time(),
                        raw_value={'mixing': True}
                    ))
                
                await asyncio.sleep(0.1)  # Čítanie každých 100ms
                
            except Exception as e:
                logger.error(f"❌ Chyba pri čítaní akcelerometra: {e}")
                await asyncio.sleep(1)

    async def read_weight(self) -> float:
        """Čítanie váhy"""
        if self.simulation_mode:
            return await self.simulate_weight_reading()
        else:
            return await self.read_bluetooth_scale()

    async def read_raw_weight(self) -> float:
        """Čítanie surovej váhy (bez kalibrácie)"""
        if self.simulation_mode:
            return random.uniform(0, 10)
        else:
            # Implementácia čítania z Bluetooth váhy
            return 0.0

    async def read_bluetooth_scale(self) -> float:
        """Čítanie z Bluetooth váhy"""
        try:
            if not self.bluetooth_scale['connected']:
                return 0.0
            
            # V reálnej implementácii by sa tu čítalo z Bluetooth váhy
            raw_weight = await self.read_raw_weight()
            calibrated_weight = raw_weight - self.weight_calibration_offset
            
            return max(0.0, calibrated_weight)
            
        except Exception as e:
            logger.error(f"❌ Chyba pri čítaní Bluetooth váhy: {e}")
            return 0.0

    async def simulate_weight_reading(self) -> float:
        """Simulácia čítania váhy"""
        # Simulácia postupného pridávania ingrediencií
        if hasattr(self, 'target_weight') and self.target_weight > 0:
            # Postupné približovanie k cieľovej hmotnosti
            diff = self.target_weight - self.simulated_weight
            if abs(diff) > 0.1:
                self.simulated_weight += diff * 0.1 + random.uniform(-0.05, 0.05)
            else:
                self.simulated_weight = self.target_weight + random.uniform(-0.02, 0.02)
        
        return max(0.0, self.simulated_weight)

    async def read_nfc(self) -> Optional[str]:
        """Čítanie NFC čipu"""
        if self.simulation_mode:
            return await self.simulate_nfc_reading()
        else:
            return await self.read_rc522()

    async def read_rc522(self) -> Optional[str]:
        """Čítanie z RC522 NFC čítačky"""
        try:
            # Implementácia čítania RC522
            # Toto je zjednodušená verzia
            
            # Kontrola prítomnosti karty
            if GPIO.input(self.nfc_reader['pin_irq']) == GPIO.LOW:
                # Karta detekovaná
                return "nfc_card_001"
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Chyba pri čítaní RC522: {e}")
            return None

    async def simulate_nfc_reading(self) -> Optional[str]:
        """Simulácia čítania NFC"""
        # Simulácia občasnej detekcie NFC
        if random.random() < 0.1:  # 10% šanca na detekciu
            return "simulated_nfc_001"
        return None

    async def read_accelerometer(self) -> Dict[str, float]:
        """Čítanie akcelerometra"""
        if self.simulation_mode:
            return await self.simulate_accelerometer_reading()
        else:
            return await self.read_mpu6050()

    async def read_raw_accelerometer(self) -> Dict[str, int]:
        """Čítanie surových dát z akcelerometra"""
        try:
            # Čítanie z MPU6050
            bus = self.accelerometer['bus']
            addr = self.accelerometer['address']
            
            # Čítanie X, Y, Z osí
            x_h = bus.read_byte_data(addr, 0x3B)
            x_l = bus.read_byte_data(addr, 0x3C)
            y_h = bus.read_byte_data(addr, 0x3D)
            y_l = bus.read_byte_data(addr, 0x3E)
            z_h = bus.read_byte_data(addr, 0x3F)
            z_l = bus.read_byte_data(addr, 0x40)
            
            # Konverzia na 16-bit hodnoty
            x = (x_h << 8) | x_l
            y = (y_h << 8) | y_l
            z = (z_h << 8) | z_l
            
            # Konverzia na signed hodnoty
            if x > 32767:
                x -= 65536
            if y > 32767:
                y -= 65536
            if z > 32767:
                z -= 65536
            
            return {'x': x, 'y': y, 'z': z}
            
        except Exception as e:
            logger.error(f"❌ Chyba pri čítaní MPU6050: {e}")
            return {'x': 0, 'y': 0, 'z': 0}

    async def read_mpu6050(self) -> Dict[str, float]:
        """Čítanie z MPU6050 akcelerometra"""
        try:
            raw_data = await self.read_raw_accelerometer()
            
            # Aplikácia kalibrácie
            calibrated_data = {
                'x': (raw_data['x'] - self.accelerometer_calibration['x']) / 16384.0,
                'y': (raw_data['y'] - self.accelerometer_calibration['y']) / 16384.0,
                'z': (raw_data['z'] - self.accelerometer_calibration['z']) / 16384.0
            }
            
            return calibrated_data
            
        except Exception as e:
            logger.error(f"❌ Chyba pri čítaní akcelerometra: {e}")
            return {'x': 0.0, 'y': 0.0, 'z': 0.0}

    async def simulate_accelerometer_reading(self) -> Dict[str, float]:
        """Simulácia čítania akcelerometra"""
        if self.simulated_mixing:
            # Simulácia miešania
            angle = time.time() * 5  # Rýchlosť rotácie
            return {
                'x': 0.5 * random.uniform(0.8, 1.2) * (1 + 0.3 * random.random()),
                'y': 0.5 * random.uniform(0.8, 1.2) * (1 + 0.3 * random.random()),
                'z': 0.1 * random.uniform(-1, 1)
            }
        else:
            # Simulácia pokoja
            return {
                'x': 0.0 + random.uniform(-0.05, 0.05),
                'y': 0.0 + random.uniform(-0.05, 0.05),
                'z': 1.0 + random.uniform(-0.05, 0.05)
            }

    def detect_mixing(self, accel_data: Dict[str, float]) -> bool:
        """Detekcia miešania na základe akcelerometra"""
        try:
            # Výpočet celkovej akcelerácie
            total_accel = (accel_data['x']**2 + accel_data['y']**2 + accel_data['z']**2)**0.5
            
            # Detekcia miešania ak je akcelerácia nad prahom
            mixing_threshold = 1.5
            is_mixing = total_accel > mixing_threshold
            
            # Počítanie rotácií
            if is_mixing:
                current_time = time.time()
                if current_time - self.accelerometer.get('last_rotation_time', 0) > 0.5:
                    self.accelerometer['rotation_count'] += 1
                    self.accelerometer['last_rotation_time'] = current_time
            
            return is_mixing
            
        except Exception as e:
            logger.error(f"❌ Chyba pri detekcii miešania: {e}")
            return False

    def set_weight_callback(self, callback: Callable):
        """Nastavenie callback funkcie pre váhu"""
        self.weight_callback = callback

    def set_nfc_callback(self, callback: Callable):
        """Nastavenie callback funkcie pre NFC"""
        self.nfc_callback = callback

    def set_accelerometer_callback(self, callback: Callable):
        """Nastavenie callback funkcie pre akcelerometer"""
        self.accelerometer_callback = callback

    def set_target_weight(self, weight: float):
        """Nastavenie cieľovej hmotnosti pre simuláciu"""
        self.target_weight = weight
        logger.info(f"🎯 Cieľová hmotnosť nastavená: {weight}g")

    def start_mixing_simulation(self):
        """Spustenie simulácie miešania"""
        self.simulated_mixing = True
        logger.info("🥄 Simulácia miešania spustená")

    def stop_mixing_simulation(self):
        """Zastavenie simulácie miešania"""
        self.simulated_mixing = False
        logger.info("🥄 Simulácia miešania zastavená")

    def is_healthy(self) -> bool:
        """Kontrola zdravia sensor managera"""
        return self.is_monitoring

    async def reset(self):
        """Reset sensor managera"""
        logger.info("🔄 Reset sensor managera...")
        
        await self.stop()
        await asyncio.sleep(1)
        await self.initialize()
        
        logger.info("✅ Sensor manager resetovaný")

    async def stop(self):
        """Zastavenie monitorovania senzorov"""
        logger.info("🛑 Zastavujem monitorovanie senzorov...")
        
        self.is_monitoring = False
        
        if not self.simulation_mode and GPIO_AVAILABLE:
            GPIO.cleanup()
        
        logger.info("✅ Monitorovanie senzorov zastavené")
