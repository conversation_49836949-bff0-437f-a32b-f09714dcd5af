#!/usr/bin/env python3
"""
Čarodejnícka Alchymistická Hra - LED Controller
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> WS2812B LED pásika pre vizuálne efekty
"""

import asyncio
import logging
import time
import math
import random
from typing import List, Tuple, Dict, Any, Optional
from dataclasses import dataclass

try:
    import neopixel
    import board
    NEOPIXEL_AVAILABLE = True
except ImportError:
    NEOPIXEL_AVAILABLE = False
    logging.warning("⚠️ NeoPixel knižnice nie sú dostupné - LED efekty budú simulované")

logger = logging.getLogger(__name__)


@dataclass
class Color:
    """RGB farba"""
    r: int
    g: int
    b: int
    
    def __iter__(self):
        return iter((self.r, self.g, self.b))
    
    def __getitem__(self, index):
        return (self.r, self.g, self.b)[index]


class LEDController:
    """Kontrolér pre WS2812B LED pásik"""
    
    # Predefinované farby
    COLORS = {
        'red': Color(255, 0, 0),
        'green': Color(0, 255, 0),
        'blue': Color(0, 0, 255),
        'yellow': Color(255, 255, 0),
        'purple': Color(128, 0, 128),
        'orange': Color(255, 165, 0),
        'white': Color(255, 255, 255),
        'black': Color(0, 0, 0),
        'gold': Color(255, 215, 0),
        'silver': Color(192, 192, 192),
        'pink': Color(255, 192, 203),
        'cyan': Color(0, 255, 255)
    }
    
    def __init__(self, pin: int, num_leds: int):
        self.pin = pin
        self.num_leds = num_leds
        self.pixels = None
        self.simulation_mode = not NEOPIXEL_AVAILABLE
        self.is_running = False
        self.current_effect = None
        self.effect_queue = []
        
        # Simulačné dáta
        self.simulated_pixels = [(0, 0, 0)] * num_leds
        
        logger.info(f"💡 LED Controller inicializovaný: {num_leds} LEDs na pine {pin}")

    async def initialize(self):
        """Inicializácia LED pásika"""
        try:
            if self.simulation_mode:
                logger.info("💡 LED Controller v simulačnom režime")
                return
            
            logger.info("💡 Inicializujem LED pásik...")
            
            # Inicializácia NeoPixel
            if self.pin == 18:
                pin_obj = board.D18
            elif self.pin == 12:
                pin_obj = board.D12
            else:
                raise ValueError(f"Nepodporovaný pin: {self.pin}")
            
            self.pixels = neopixel.NeoPixel(
                pin_obj, 
                self.num_leds, 
                brightness=0.8, 
                auto_write=False
            )
            
            # Test LED pásika
            await self.test_leds()
            
            logger.info("✅ LED pásik inicializovaný")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri inicializácii LED pásika: {e}")
            self.simulation_mode = True
            logger.info("💡 Prepínam na simulačný režim")

    async def test_leds(self):
        """Test LED pásika"""
        try:
            logger.info("🧪 Testujem LED pásik...")
            
            # Postupné rozsvietenie všetkých LEDs
            for i in range(self.num_leds):
                await self.set_pixel(i, self.COLORS['red'])
                await self.show()
                await asyncio.sleep(0.05)
            
            await asyncio.sleep(0.5)
            
            # Vypnutie všetkých LEDs
            await self.clear()
            
            logger.info("✅ Test LED pásika dokončený")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri teste LED pásika: {e}")

    async def set_pixel(self, index: int, color: Color):
        """Nastavenie farby jednej LED"""
        if 0 <= index < self.num_leds:
            if self.simulation_mode:
                self.simulated_pixels[index] = (color.r, color.g, color.b)
            else:
                self.pixels[index] = (color.r, color.g, color.b)

    async def set_all_pixels(self, color: Color):
        """Nastavenie farby všetkých LEDs"""
        for i in range(self.num_leds):
            await self.set_pixel(i, color)

    async def show(self):
        """Zobrazenie zmien na LED pásiku"""
        if not self.simulation_mode and self.pixels:
            self.pixels.show()

    async def clear(self):
        """Vypnutie všetkých LEDs"""
        await self.set_all_pixels(self.COLORS['black'])
        await self.show()

    async def start_effects_loop(self):
        """Spustenie slučky pre efekty"""
        logger.info("🔄 LED efekty slučka spustená")
        
        self.is_running = True
        
        while self.is_running:
            try:
                # Spracovanie fronty efektov
                if self.effect_queue:
                    effect_data = self.effect_queue.pop(0)
                    await self.execute_effect(effect_data)
                
                await asyncio.sleep(0.05)  # 20 FPS
                
            except Exception as e:
                logger.error(f"❌ Chyba v LED efektoch: {e}")
                await asyncio.sleep(1)

    async def execute_effect(self, effect_data: Dict[str, Any]):
        """Vykonanie LED efektu"""
        try:
            effect_name = effect_data.get('name', 'unknown')
            duration = effect_data.get('duration', 1.0)
            params = effect_data.get('params', {})
            
            logger.info(f"✨ Vykonávam LED efekt: {effect_name}")
            
            # Výber efektu
            if effect_name == 'startup':
                await self.effect_startup(duration, **params)
            elif effect_name == 'shutdown':
                await self.effect_shutdown(duration, **params)
            elif effect_name == 'ingredient_correct':
                await self.effect_ingredient_correct(duration, **params)
            elif effect_name == 'ingredient_wrong':
                await self.effect_ingredient_wrong(duration, **params)
            elif effect_name == 'mixing_active':
                await self.effect_mixing_active(duration, **params)
            elif effect_name == 'mixing_complete':
                await self.effect_mixing_complete(duration, **params)
            elif effect_name == 'final_success':
                await self.effect_final_success(duration, **params)
            elif effect_name == 'final_failure':
                await self.effect_final_failure(duration, **params)
            elif effect_name == 'waiting':
                await self.effect_waiting(duration, **params)
            elif effect_name == 'rainbow':
                await self.effect_rainbow(duration, **params)
            elif effect_name == 'pulse':
                await self.effect_pulse(duration, **params)
            elif effect_name == 'sparkle':
                await self.effect_sparkle(duration, **params)
            else:
                logger.warning(f"⚠️ Neznámy LED efekt: {effect_name}")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri vykonávaní efektu {effect_data.get('name', 'unknown')}: {e}")

    async def effect_startup(self, duration: float, **params):
        """Efekt spustenia"""
        steps = int(duration * 20)  # 20 FPS
        
        for step in range(steps):
            progress = step / steps
            
            # Postupné rozsvietenie zo stredu
            center = self.num_leds // 2
            radius = int(progress * center)
            
            await self.clear()
            
            for i in range(max(0, center - radius), min(self.num_leds, center + radius + 1)):
                brightness = 1.0 - abs(i - center) / (radius + 1)
                color = Color(
                    int(255 * brightness),
                    int(215 * brightness),
                    0
                )
                await self.set_pixel(i, color)
            
            await self.show()
            await asyncio.sleep(duration / steps)

    async def effect_shutdown(self, duration: float, **params):
        """Efekt vypnutia"""
        steps = int(duration * 20)
        
        for step in range(steps):
            progress = 1.0 - (step / steps)
            
            # Postupné zhasínanie
            for i in range(self.num_leds):
                color = Color(
                    int(255 * progress),
                    int(215 * progress),
                    0
                )
                await self.set_pixel(i, color)
            
            await self.show()
            await asyncio.sleep(duration / steps)
        
        await self.clear()

    async def effect_ingredient_correct(self, duration: float, **params):
        """Efekt správnej ingrediencie"""
        steps = int(duration * 20)
        
        for step in range(steps):
            # Zelené pulzovanie
            brightness = 0.5 + 0.5 * math.sin(step * 0.5)
            
            color = Color(
                0,
                int(255 * brightness),
                0
            )
            
            await self.set_all_pixels(color)
            await self.show()
            await asyncio.sleep(duration / steps)

    async def effect_ingredient_wrong(self, duration: float, **params):
        """Efekt nesprávnej ingrediencie"""
        steps = int(duration * 10)  # Rýchlejšie blikanie
        
        for step in range(steps):
            if step % 2 == 0:
                await self.set_all_pixels(self.COLORS['red'])
            else:
                await self.clear()
            
            await self.show()
            await asyncio.sleep(duration / steps)

    async def effect_mixing_active(self, duration: float, **params):
        """Efekt aktívneho miešania"""
        direction = params.get('direction', 'clockwise')
        speed = params.get('speed', 'medium')
        
        # Nastavenie rýchlosti
        speed_multiplier = {'slow': 0.5, 'medium': 1.0, 'fast': 2.0}.get(speed, 1.0)
        steps = int(duration * 20 * speed_multiplier)
        
        for step in range(steps):
            # Rotujúca dúha
            offset = step * (1 if direction == 'clockwise' else -1)
            
            for i in range(self.num_leds):
                hue = ((i + offset) * 360 / self.num_leds) % 360
                color = self.hsv_to_rgb(hue, 1.0, 1.0)
                await self.set_pixel(i, color)
            
            await self.show()
            await asyncio.sleep(duration / steps)

    async def effect_mixing_complete(self, duration: float, **params):
        """Efekt dokončeného miešania"""
        steps = int(duration * 20)
        
        for step in range(steps):
            # Zlaté trblietanie
            for i in range(self.num_leds):
                if random.random() < 0.3:  # 30% šanca na trblietanie
                    brightness = random.uniform(0.5, 1.0)
                    color = Color(
                        int(255 * brightness),
                        int(215 * brightness),
                        0
                    )
                    await self.set_pixel(i, color)
                else:
                    await self.set_pixel(i, self.COLORS['black'])
            
            await self.show()
            await asyncio.sleep(duration / steps)

    async def effect_final_success(self, duration: float, **params):
        """Efekt finálneho úspechu"""
        steps = int(duration * 20)
        
        for step in range(steps):
            # Zlaté vlny
            wave_position = (step * 2) % self.num_leds
            
            await self.clear()
            
            for i in range(self.num_leds):
                distance = min(abs(i - wave_position), abs(i - wave_position + self.num_leds))
                if distance < 5:
                    brightness = 1.0 - (distance / 5.0)
                    color = Color(
                        int(255 * brightness),
                        int(215 * brightness),
                        0
                    )
                    await self.set_pixel(i, color)
            
            await self.show()
            await asyncio.sleep(duration / steps)

    async def effect_final_failure(self, duration: float, **params):
        """Efekt finálneho neúspechu"""
        steps = int(duration * 20)
        
        for step in range(steps):
            # Červeno-oranžové blikanie
            if step % 10 < 5:
                color = self.COLORS['red']
            else:
                color = self.COLORS['orange']
            
            await self.set_all_pixels(color)
            await self.show()
            await asyncio.sleep(duration / steps)

    async def effect_waiting(self, duration: float, **params):
        """Efekt čakania"""
        steps = int(duration * 20)
        
        for step in range(steps):
            # Modré dýchanie
            brightness = 0.3 + 0.7 * (0.5 + 0.5 * math.sin(step * 0.2))
            
            color = Color(
                0,
                0,
                int(255 * brightness)
            )
            
            await self.set_all_pixels(color)
            await self.show()
            await asyncio.sleep(duration / steps)

    async def effect_rainbow(self, duration: float, **params):
        """Dúhový efekt"""
        steps = int(duration * 20)
        
        for step in range(steps):
            for i in range(self.num_leds):
                hue = (i * 360 / self.num_leds + step * 5) % 360
                color = self.hsv_to_rgb(hue, 1.0, 1.0)
                await self.set_pixel(i, color)
            
            await self.show()
            await asyncio.sleep(duration / steps)

    async def effect_pulse(self, duration: float, **params):
        """Pulzujúci efekt"""
        color_name = params.get('color', 'gold')
        base_color = self.COLORS.get(color_name, self.COLORS['gold'])
        
        steps = int(duration * 20)
        
        for step in range(steps):
            brightness = 0.2 + 0.8 * (0.5 + 0.5 * math.sin(step * 0.3))
            
            color = Color(
                int(base_color.r * brightness),
                int(base_color.g * brightness),
                int(base_color.b * brightness)
            )
            
            await self.set_all_pixels(color)
            await self.show()
            await asyncio.sleep(duration / steps)

    async def effect_sparkle(self, duration: float, **params):
        """Trblietavý efekt"""
        color_name = params.get('color', 'gold')
        base_color = self.COLORS.get(color_name, self.COLORS['gold'])
        
        steps = int(duration * 20)
        
        for step in range(steps):
            await self.clear()
            
            # Náhodné trblietanie
            num_sparkles = random.randint(3, 8)
            for _ in range(num_sparkles):
                pixel = random.randint(0, self.num_leds - 1)
                brightness = random.uniform(0.5, 1.0)
                
                color = Color(
                    int(base_color.r * brightness),
                    int(base_color.g * brightness),
                    int(base_color.b * brightness)
                )
                
                await self.set_pixel(pixel, color)
            
            await self.show()
            await asyncio.sleep(duration / steps)

    def hsv_to_rgb(self, h: float, s: float, v: float) -> Color:
        """Konverzia HSV na RGB"""
        h = h / 360.0
        i = int(h * 6.0)
        f = (h * 6.0) - i
        p = v * (1.0 - s)
        q = v * (1.0 - s * f)
        t = v * (1.0 - s * (1.0 - f))
        
        i = i % 6
        
        if i == 0:
            r, g, b = v, t, p
        elif i == 1:
            r, g, b = q, v, p
        elif i == 2:
            r, g, b = p, v, t
        elif i == 3:
            r, g, b = p, q, v
        elif i == 4:
            r, g, b = t, p, v
        elif i == 5:
            r, g, b = v, p, q
        
        return Color(int(r * 255), int(g * 255), int(b * 255))

    async def play_effect(self, effect_name: str, duration: float = 1.0, **params):
        """Pridanie efektu do fronty"""
        effect_data = {
            'name': effect_name,
            'duration': duration,
            'params': params
        }
        
        self.effect_queue.append(effect_data)
        logger.info(f"💡 Efekt pridaný do fronty: {effect_name}")

    async def stop_current_effect(self):
        """Zastavenie aktuálneho efektu"""
        self.current_effect = None
        await self.clear()

    async def clear_effect_queue(self):
        """Vymazanie fronty efektov"""
        self.effect_queue.clear()
        logger.info("💡 Fronta efektov vymazaná")

    def is_healthy(self) -> bool:
        """Kontrola zdravia LED kontroléra"""
        return self.is_running

    async def reset(self):
        """Reset LED kontroléra"""
        logger.info("🔄 Reset LED kontroléra...")
        
        await self.clear_effect_queue()
        await self.clear()
        
        logger.info("✅ LED kontrolér resetovaný")

    async def cleanup(self):
        """Vyčistenie zdrojov"""
        logger.info("🧹 Čistím LED kontrolér...")
        
        self.is_running = False
        await self.clear()
        
        if not self.simulation_mode and self.pixels:
            self.pixels.deinit()
        
        logger.info("✅ LED kontrolér vyčistený")
