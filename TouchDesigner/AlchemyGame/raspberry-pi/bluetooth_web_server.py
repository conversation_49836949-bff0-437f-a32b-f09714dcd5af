#!/usr/bin/env python3
"""
Čarodejnícka Alchymistická Hra - BLE Server
Bluetooth Low Energy server kompatibilný s Web Bluetooth API
"""

import asyncio
import json
import logging
import time
from typing import Optional, Callable, Dict, Any
from dataclasses import dataclass

try:
    import dbus
    import dbus.exceptions
    import dbus.mainloop.glib
    import dbus.service
    from gi.repository import GLib
    DBUS_AVAILABLE = True
except ImportError:
    DBUS_AVAILABLE = False
    logging.warning("⚠️ D-Bus knižnice nie sú dostupné - BLE bude simulované")

logger = logging.getLogger(__name__)


@dataclass
class BLEMessage:
    """Štruktúra BLE správy"""
    action: str
    data: Dict[str, Any]
    message_id: Optional[str] = None
    timestamp: Optional[float] = None
    response_to_id: Optional[str] = None


class AlchemyBLEServer:
    """BLE Server pre Čarodejnícku Alchymistickú Hru"""
    
    # BLE Service a Characteristic UUIDs
    SERVICE_UUID = "12345678-1234-5678-9012-123456789abc"
    CHARACTERISTIC_UUID = "*************-8765-2109-cba987654321"
    
    def __init__(self, game_logic, config):
        self.game_logic = game_logic
        self.config = config
        self.is_running_flag = False
        self.connected_devices = set()
        self.message_callback: Optional[Callable] = None
        
        # D-Bus objekty
        self.bus = None
        self.adapter = None
        self.service_manager = None
        self.advertisement = None
        self.service = None
        self.characteristic = None
        
        # Simulácia pre testovanie
        self.simulation_mode = not DBUS_AVAILABLE
        self.simulated_connections = set()
        
        logger.info("🔵 BLE Server inicializovaný")

    async def initialize(self):
        """Inicializácia BLE servera"""
        try:
            if self.simulation_mode:
                logger.info("🔵 BLE Server v simulačnom režime")
                return
            
            logger.info("🔵 Inicializujem BLE Server...")
            
            # Inicializácia D-Bus
            dbus.mainloop.glib.DBusGMainLoop(set_as_default=True)
            self.bus = dbus.SystemBus()
            
            # Získanie Bluetooth adaptéra
            await self.setup_bluetooth_adapter()
            
            # Vytvorenie BLE služby
            await self.create_ble_service()
            
            # Vytvorenie charakteristiky
            await self.create_characteristic()
            
            # Registrácia služby
            await self.register_service()
            
            logger.info("✅ BLE Server inicializovaný")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri inicializácii BLE servera: {e}")
            self.simulation_mode = True
            logger.info("🔵 Prepínam na simulačný režim")

    async def setup_bluetooth_adapter(self):
        """Nastavenie Bluetooth adaptéra"""
        try:
            # Získanie adaptéra
            adapter_path = "/org/bluez/hci0"
            adapter_obj = self.bus.get_object("org.bluez", adapter_path)
            self.adapter = dbus.Interface(adapter_obj, "org.bluez.Adapter1")
            
            # Zapnutie adaptéra
            self.adapter.Set("org.bluez.Adapter1", "Powered", dbus.Boolean(True))
            self.adapter.Set("org.bluez.Adapter1", "Discoverable", dbus.Boolean(True))
            self.adapter.Set("org.bluez.Adapter1", "DiscoverableTimeout", dbus.UInt32(0))
            
            # Nastavenie názvu zariadenia
            self.adapter.Set("org.bluez.Adapter1", "Alias", dbus.String("AlchemyPi"))
            
            logger.info("✅ Bluetooth adaptér nastavený")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri nastavení Bluetooth adaptéra: {e}")
            raise

    async def create_ble_service(self):
        """Vytvorenie BLE služby"""
        try:
            # Implementácia BLE služby cez D-Bus
            # Toto je zjednodušená verzia - v reálnej implementácii by bolo potrebné
            # implementovať kompletné D-Bus rozhranie pre GATT služby
            
            logger.info("🔧 Vytváram BLE službu...")
            
            # Pre túto implementáciu použijeme simuláciu
            self.service = {
                'uuid': self.SERVICE_UUID,
                'primary': True,
                'characteristics': []
            }
            
            logger.info(f"✅ BLE služba vytvorená: {self.SERVICE_UUID}")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri vytváraní BLE služby: {e}")
            raise

    async def create_characteristic(self):
        """Vytvorenie BLE charakteristiky"""
        try:
            logger.info("🔧 Vytváram BLE charakteristiku...")
            
            self.characteristic = {
                'uuid': self.CHARACTERISTIC_UUID,
                'flags': ['read', 'write', 'notify'],
                'service': self.service,
                'value': b'',
                'notifying': False
            }
            
            self.service['characteristics'].append(self.characteristic)
            
            logger.info(f"✅ BLE charakteristika vytvorená: {self.CHARACTERISTIC_UUID}")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri vytváraní BLE charakteristiky: {e}")
            raise

    async def register_service(self):
        """Registrácia BLE služby"""
        try:
            logger.info("📝 Registrujem BLE službu...")
            
            # V reálnej implementácii by sa tu registrovala služba cez D-Bus
            # Pre túto verziu použijeme simuláciu
            
            logger.info("✅ BLE služba zaregistrovaná")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri registrácii BLE služby: {e}")
            raise

    async def start(self):
        """Spustenie BLE servera"""
        try:
            logger.info("🚀 Spúšťam BLE Server...")
            
            self.is_running_flag = True
            
            if self.simulation_mode:
                # Simulačný režim
                asyncio.create_task(self.simulation_loop())
            else:
                # Reálny BLE režim
                await self.start_advertising()
            
            logger.info("✅ BLE Server spustený")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri spustení BLE servera: {e}")
            raise

    async def start_advertising(self):
        """Spustenie BLE advertising"""
        try:
            logger.info("📡 Spúšťam BLE advertising...")
            
            # V reálnej implementácii by sa tu spustilo advertising cez D-Bus
            # Pre túto verziu použijeme simuláciu
            
            logger.info("✅ BLE advertising spustené")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri spustení advertising: {e}")
            raise

    async def simulation_loop(self):
        """Simulačná slučka pre testovanie bez BLE"""
        logger.info("🔄 Simulačná slučka spustená")
        
        # Simulácia pripojenia po 5 sekundách
        await asyncio.sleep(5)
        await self.simulate_device_connection("simulated_device_001")
        
        while self.is_running_flag:
            # Simulácia príjmu správ
            await self.simulate_incoming_messages()
            await asyncio.sleep(1)

    async def simulate_device_connection(self, device_id: str):
        """Simulácia pripojenia zariadenia"""
        logger.info(f"📱 Simulácia pripojenia zariadenia: {device_id}")
        self.simulated_connections.add(device_id)
        
        # Odoslanie handshake správy
        await self.handle_incoming_message({
            'action': 'handshake',
            'data': {'client': 'web-app', 'version': '1.0'},
            'message_id': 'sim_001',
            'timestamp': time.time()
        })

    async def simulate_incoming_messages(self):
        """Simulácia prichádzajúcich správ"""
        # Pre testovanie - simulujeme občasné správy
        if len(self.simulated_connections) > 0 and time.time() % 30 < 1:
            await self.handle_incoming_message({
                'action': 'ping',
                'data': {},
                'message_id': f'sim_{int(time.time())}',
                'timestamp': time.time()
            })

    async def handle_incoming_message(self, raw_message: Dict[str, Any]):
        """Spracovanie prichádzajúcej správy"""
        try:
            # Parsovanie správy
            message = BLEMessage(
                action=raw_message.get('action', ''),
                data=raw_message.get('data', {}),
                message_id=raw_message.get('message_id'),
                timestamp=raw_message.get('timestamp', time.time()),
                response_to_id=raw_message.get('response_to_id')
            )
            
            logger.info(f"📥 Prijatá správa: {message.action}")
            
            # Predanie správy hernej logike
            if self.message_callback:
                await self.message_callback(message)
            
        except Exception as e:
            logger.error(f"❌ Chyba pri spracovaní správy: {e}")

    async def send_response(self, message: BLEMessage):
        """Odoslanie odpovede na web aplikáciu"""
        try:
            # Konverzia na JSON
            response_data = {
                'status': message.action,
                'data': message.data,
                'message_id': message.message_id,
                'timestamp': message.timestamp,
                'response_to_id': message.response_to_id
            }
            
            json_data = json.dumps(response_data)
            
            if self.simulation_mode:
                logger.info(f"📤 Simulácia odoslania: {message.action}")
            else:
                # Reálne odoslanie cez BLE
                await self.send_ble_notification(json_data.encode('utf-8'))
            
            logger.info(f"📤 Odpoveď odoslaná: {message.action}")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri odosielaní odpovede: {e}")

    async def send_ble_notification(self, data: bytes):
        """Odoslanie BLE notifikácie"""
        try:
            # V reálnej implementácii by sa tu odoslala notifikácia cez D-Bus
            # Pre túto verziu len logujeme
            logger.debug(f"📡 BLE notifikácia: {len(data)} bytov")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri odosielaní BLE notifikácie: {e}")

    def set_message_callback(self, callback: Callable):
        """Nastavenie callback funkcie pre správy"""
        self.message_callback = callback
        logger.info("🔗 Message callback nastavený")

    def is_running(self) -> bool:
        """Kontrola, či server beží"""
        return self.is_running_flag

    def get_connected_devices(self) -> set:
        """Získanie zoznamu pripojených zariadení"""
        if self.simulation_mode:
            return self.simulated_connections
        else:
            return self.connected_devices

    async def stop(self):
        """Zastavenie BLE servera"""
        try:
            logger.info("🛑 Zastavujem BLE Server...")
            
            self.is_running_flag = False
            
            if not self.simulation_mode:
                # Zastavenie advertising
                await self.stop_advertising()
                
                # Odpojenie zariadení
                await self.disconnect_all_devices()
            
            # Vyčistenie simulovaných pripojení
            self.simulated_connections.clear()
            
            logger.info("✅ BLE Server zastavený")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri zastavení BLE servera: {e}")

    async def stop_advertising(self):
        """Zastavenie BLE advertising"""
        try:
            logger.info("📡 Zastavujem BLE advertising...")
            # Implementácia zastavenia advertising
            logger.info("✅ BLE advertising zastavené")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri zastavení advertising: {e}")

    async def disconnect_all_devices(self):
        """Odpojenie všetkých zariadení"""
        try:
            logger.info("📱 Odpájam všetky zariadenia...")
            
            for device in list(self.connected_devices):
                await self.disconnect_device(device)
            
            self.connected_devices.clear()
            logger.info("✅ Všetky zariadenia odpojené")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri odpájaní zariadení: {e}")

    async def disconnect_device(self, device_id: str):
        """Odpojenie konkrétneho zariadenia"""
        try:
            logger.info(f"📱 Odpájam zariadenie: {device_id}")
            
            if device_id in self.connected_devices:
                self.connected_devices.remove(device_id)
            
            logger.info(f"✅ Zariadenie odpojené: {device_id}")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri odpájaní zariadenia {device_id}: {e}")

    async def restart(self):
        """Reštart BLE servera"""
        logger.info("🔄 Reštartujem BLE Server...")
        
        await self.stop()
        await asyncio.sleep(2)
        await self.start()
        
        logger.info("✅ BLE Server reštartovaný")

    def get_status(self) -> Dict[str, Any]:
        """Získanie stavu BLE servera"""
        return {
            'running': self.is_running_flag,
            'simulation_mode': self.simulation_mode,
            'connected_devices': len(self.get_connected_devices()),
            'service_uuid': self.SERVICE_UUID,
            'characteristic_uuid': self.CHARACTERISTIC_UUID
        }
