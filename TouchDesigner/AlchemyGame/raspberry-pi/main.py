#!/usr/bin/env python3
"""
Čarodejnícka Alchymistická Hra - Raspberry Pi Kontrolér
Hlavný súbor pre spustenie všetkých komponentov
"""

import asyncio
import logging
import signal
import sys
import time
from threading import Thread, Event
from typing import Optional

# Import vlastných modulov
from bluetooth_web_server import AlchemyBLEServer
from sensor_manager import SensorManager
from led_controller import LEDController
from game_logic import GameLogic
from config import Config

# Nastavenie logovania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/alchemy_game.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


class AlchemyGameController:
    """Hlavný kontrolér pre Čarodejnícku Alchymistickú Hru"""
    
    def __init__(self):
        self.config = Config()
        self.running = False
        self.shutdown_event = Event()
        
        # Komponenty
        self.ble_server: Optional[AlchemyBLEServer] = None
        self.sensor_manager: Optional[SensorManager] = None
        self.led_controller: Optional[LEDController] = None
        self.game_logic: Optional[GameLogic] = None
        
        # Vlákna
        self.sensor_thread: Optional[Thread] = None
        self.led_thread: Optional[Thread] = None
        
        logger.info("🧙‍♂️ Inicializujem Čarodejnícku Alchymistickú Hru...")

    async def initialize(self):
        """Inicializácia všetkých komponentov"""
        try:
            logger.info("🔧 Inicializujem komponenty...")
            
            # Inicializácia LED kontroléra
            self.led_controller = LEDController(
                pin=self.config.LED_PIN,
                num_leds=self.config.NUM_LEDS
            )
            await self.led_controller.initialize()
            
            # Inicializácia sensor managera
            self.sensor_manager = SensorManager(self.config)
            await self.sensor_manager.initialize()
            
            # Inicializácia hernej logiky
            self.game_logic = GameLogic(
                sensor_manager=self.sensor_manager,
                led_controller=self.led_controller,
                config=self.config
            )
            
            # Inicializácia BLE servera
            self.ble_server = AlchemyBLEServer(
                game_logic=self.game_logic,
                config=self.config
            )
            await self.ble_server.initialize()
            
            # Nastavenie callback funkcií
            self.setup_callbacks()
            
            logger.info("✅ Všetky komponenty inicializované")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri inicializácii: {e}")
            raise

    def setup_callbacks(self):
        """Nastavenie callback funkcií medzi komponentmi"""
        
        # Callback pre BLE server -> Game Logic
        self.ble_server.set_message_callback(self.game_logic.handle_web_message)
        
        # Callback pre Game Logic -> BLE server
        self.game_logic.set_response_callback(self.ble_server.send_response)
        
        # Callback pre Sensor Manager -> Game Logic
        self.sensor_manager.set_weight_callback(self.game_logic.handle_weight_reading)
        self.sensor_manager.set_nfc_callback(self.game_logic.handle_nfc_reading)
        self.sensor_manager.set_accelerometer_callback(self.game_logic.handle_accelerometer_reading)
        
        logger.info("🔗 Callback funkcie nastavené")

    async def start(self):
        """Spustenie všetkých služieb"""
        try:
            logger.info("🚀 Spúšťam služby...")
            
            self.running = True
            
            # Spustenie BLE servera
            await self.ble_server.start()
            
            # Spustenie sensor managera v samostatnom vlákne
            self.sensor_thread = Thread(
                target=self.run_sensor_manager,
                daemon=True
            )
            self.sensor_thread.start()
            
            # Spustenie LED kontroléra v samostatnom vlákne
            self.led_thread = Thread(
                target=self.run_led_controller,
                daemon=True
            )
            self.led_thread.start()
            
            # Úvodný LED efekt
            await self.led_controller.play_effect('startup', duration=3)
            
            logger.info("✅ Všetky služby spustené")
            logger.info("🎮 Hra je pripravená!")
            
            # Hlavná slučka
            await self.main_loop()
            
        except Exception as e:
            logger.error(f"❌ Chyba pri spustení: {e}")
            await self.shutdown()
            raise

    def run_sensor_manager(self):
        """Spustenie sensor managera v samostatnom vlákne"""
        try:
            asyncio.run(self.sensor_manager.start_monitoring())
        except Exception as e:
            logger.error(f"❌ Chyba v sensor manageri: {e}")

    def run_led_controller(self):
        """Spustenie LED kontroléra v samostatnom vlákne"""
        try:
            asyncio.run(self.led_controller.start_effects_loop())
        except Exception as e:
            logger.error(f"❌ Chyba v LED kontroléri: {e}")

    async def main_loop(self):
        """Hlavná slučka aplikácie"""
        logger.info("🔄 Hlavná slučka spustená")
        
        try:
            while self.running and not self.shutdown_event.is_set():
                # Kontrola stavu komponentov
                await self.check_component_health()
                
                # Spracovanie herných udalostí
                await self.game_logic.process_events()
                
                # Krátka pauza
                await asyncio.sleep(0.1)
                
        except KeyboardInterrupt:
            logger.info("⌨️ Prerušenie z klávesnice")
        except Exception as e:
            logger.error(f"❌ Chyba v hlavnej slučke: {e}")
        finally:
            await self.shutdown()

    async def check_component_health(self):
        """Kontrola zdravia všetkých komponentov"""
        try:
            # Kontrola BLE servera
            if not self.ble_server.is_running():
                logger.warning("⚠️ BLE server nie je aktívny")
                await self.ble_server.restart()
            
            # Kontrola sensor managera
            if not self.sensor_manager.is_healthy():
                logger.warning("⚠️ Sensor manager má problémy")
                await self.sensor_manager.reset()
            
            # Kontrola LED kontroléra
            if not self.led_controller.is_healthy():
                logger.warning("⚠️ LED kontrolér má problémy")
                await self.led_controller.reset()
                
        except Exception as e:
            logger.error(f"❌ Chyba pri kontrole zdravia: {e}")

    async def shutdown(self):
        """Bezpečné vypnutie všetkých komponentov"""
        logger.info("🛑 Vypínam systém...")
        
        self.running = False
        self.shutdown_event.set()
        
        try:
            # Vypnutie LED efektu
            if self.led_controller:
                await self.led_controller.play_effect('shutdown', duration=2)
                await self.led_controller.cleanup()
            
            # Vypnutie BLE servera
            if self.ble_server:
                await self.ble_server.stop()
            
            # Vypnutie sensor managera
            if self.sensor_manager:
                await self.sensor_manager.stop()
            
            # Čakanie na ukončenie vlákien
            if self.sensor_thread and self.sensor_thread.is_alive():
                self.sensor_thread.join(timeout=5)
            
            if self.led_thread and self.led_thread.is_alive():
                self.led_thread.join(timeout=5)
            
            logger.info("✅ Systém bezpečne vypnutý")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri vypínaní: {e}")

    def signal_handler(self, signum, frame):
        """Handler pre systémové signály"""
        logger.info(f"📡 Prijatý signál {signum}")
        self.shutdown_event.set()


async def main():
    """Hlavná funkcia"""
    controller = AlchemyGameController()
    
    # Nastavenie signal handlerov
    signal.signal(signal.SIGINT, controller.signal_handler)
    signal.signal(signal.SIGTERM, controller.signal_handler)
    
    try:
        # Inicializácia a spustenie
        await controller.initialize()
        await controller.start()
        
    except KeyboardInterrupt:
        logger.info("⌨️ Aplikácia prerušená používateľom")
    except Exception as e:
        logger.error(f"❌ Kritická chyba: {e}")
        sys.exit(1)
    finally:
        await controller.shutdown()


if __name__ == "__main__":
    try:
        # Kontrola, či beží ako root (potrebné pre GPIO)
        if sys.platform.startswith('linux') and os.geteuid() != 0:
            logger.error("❌ Aplikácia musí bežať ako root pre prístup k GPIO")
            sys.exit(1)
        
        # Spustenie hlavnej funkcie
        asyncio.run(main())
        
    except Exception as e:
        logger.error(f"❌ Nepodarilo sa spustiť aplikáciu: {e}")
        sys.exit(1)
