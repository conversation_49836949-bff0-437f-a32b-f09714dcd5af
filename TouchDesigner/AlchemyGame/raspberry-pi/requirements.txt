# Čarodejn<PERSON>cka Alchymistická Hra - Python Dependencies
# Raspberry Pi kontrolér

# GPIO a hardware ovládanie
RPi.GPIO==0.7.1
gpiozero==1.6.2

# LED pásik WS2812B
adafruit-circuitpython-neopixel==6.3.9
adafruit-blinka==8.22.2

# I2C a SPI komunikácia
adafruit-circuitpython-busdevice==5.2.6
smbus2==0.4.2
spidev==3.6

# Bluetooth Low Energy
pybluez==0.23
dbus-python==1.3.2
PyGObject==3.44.1

# NFC čítačka RC522
mfrc522==0.0.7
pi-rc522==2.2.1

# Akcelerometer MPU6050
mpu6050-raspberrypi==1.2.0
adafruit-circuitpython-mpu6050==1.1.6

# Asyncio a networking
aiohttp==3.8.5
asyncio-mqtt==0.13.0
websockets==11.0.3

# Dáta a serializácia
pydantic==2.3.0
dataclasses-json==0.6.1
marshmallow==3.20.1

# Logging a monitoring
colorlog==6.7.0
psutil==5.9.5
watchdog==3.0.0

# Matematika a spracovanie signálov
numpy==1.24.4
scipy==1.11.2

# Konfigurácia
python-dotenv==1.0.0
configparser==6.0.0
pyyaml==6.0.1

# Testovanie a development
pytest==7.4.2
pytest-asyncio==0.21.1
pytest-mock==3.11.1
black==23.7.0
flake8==6.0.0

# Systémové utility
systemd-python==235
crontab==1.0.1

# Bezpečnosť
cryptography==41.0.4
bcrypt==4.0.1

# Debugging a profiling
memory-profiler==0.61.0
py-spy==0.3.14

# Voliteľné - pre pokročilé funkcie
# opencv-python==********  # Pre computer vision (veľký balík)
# tensorflow-lite==2.13.0  # Pre AI funkcie (veľký balík)
# pillow==10.0.0  # Pre spracovanie obrázkov

# Poznámky k inštalácii:
# 1. Niektoré balíky vyžadujú systémové závislosti
# 2. Pre GPIO funkcie musí aplikácia bežať ako root
# 3. Bluetooth vyžaduje správne nastavenie systému
# 4. LED pásik vyžaduje PWM pin (GPIO 18 alebo 12)
# 5. I2C a SPI musia byť povolené v raspi-config
