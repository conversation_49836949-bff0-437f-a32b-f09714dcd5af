#!/usr/bin/env python3
"""
Čarodejnícka Alchymistická Hra - Herná logika
Spracovanie herných udalostí a koordinácia komponentov
"""

import asyncio
import logging
import time
import json
from typing import Optional, Callable, Dict, Any, List
from dataclasses import dataclass
from enum import Enum

from bluetooth_web_server import BLEMessage
from sensor_manager import SensorReading

logger = logging.getLogger(__name__)


class GameState(Enum):
    """Stavy hry"""
    IDLE = "idle"
    WAITING_FOR_CONNECTION = "waiting_connection"
    CONNECTED = "connected"
    GAME_ACTIVE = "game_active"
    STEP_IN_PROGRESS = "step_in_progress"
    GAME_COMPLETED = "game_completed"
    ERROR = "error"


@dataclass
class GameSession:
    """Herná session"""
    session_id: str
    recipe_id: Optional[int] = None
    current_step: int = 0
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    score: int = 0
    errors: int = 0
    state: GameState = GameState.IDLE


class GameLogic:
    """Hlavná herná logika"""
    
    def __init__(self, sensor_manager, led_controller, config):
        self.sensor_manager = sensor_manager
        self.led_controller = led_controller
        self.config = config
        
        self.current_session: Optional[GameSession] = None
        self.game_state = GameState.IDLE
        self.response_callback: Optional[Callable] = None
        
        # Aktuálny krok
        self.current_step_data = None
        self.step_start_time = None
        self.step_timeout_task = None
        
        # Fronta udalostí
        self.event_queue = []
        
        logger.info("🎮 Herná logika inicializovaná")

    def set_response_callback(self, callback: Callable):
        """Nastavenie callback funkcie pre odpovede"""
        self.response_callback = callback
        logger.info("🔗 Response callback nastavený")

    async def handle_web_message(self, message: BLEMessage):
        """Spracovanie správy z webovej aplikácie"""
        try:
            logger.info(f"📥 Spracovávam správu: {message.action}")
            
            # Routing správ
            if message.action == 'handshake':
                await self.handle_handshake(message)
            elif message.action == 'check_weight':
                await self.handle_check_weight(message)
            elif message.action == 'start_mixing':
                await self.handle_start_mixing(message)
            elif message.action == 'activate_effect':
                await self.handle_activate_effect(message)
            elif message.action == 'ping':
                await self.handle_ping(message)
            else:
                logger.warning(f"⚠️ Neznáma akcia: {message.action}")
                await self.send_error_response(message, f"Neznáma akcia: {message.action}")
                
        except Exception as e:
            logger.error(f"❌ Chyba pri spracovaní správy: {e}")
            await self.send_error_response(message, str(e))

    async def handle_handshake(self, message: BLEMessage):
        """Spracovanie handshake správy"""
        try:
            client_info = message.data
            logger.info(f"🤝 Handshake od klienta: {client_info}")
            
            # Zmena stavu na pripojené
            self.game_state = GameState.CONNECTED
            
            # Spustenie čakacieho LED efektu
            await self.led_controller.play_effect('waiting', duration=float('inf'))
            
            # Odpoveď
            response = BLEMessage(
                action='handshake_response',
                data={
                    'server': 'raspberry-pi',
                    'version': '1.0',
                    'status': 'ready',
                    'capabilities': ['weight', 'nfc', 'accelerometer', 'led', 'fog']
                },
                message_id=self.generate_message_id(),
                response_to_id=message.message_id
            )
            
            await self.send_response(response)
            logger.info("✅ Handshake dokončený")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri handshake: {e}")
            await self.send_error_response(message, str(e))

    async def handle_check_weight(self, message: BLEMessage):
        """Spracovanie požiadavky na kontrolu váhy"""
        try:
            data = message.data
            ingredient = data.get('ingredient')
            target_weight = data.get('target_weight')
            tolerance = data.get('tolerance')
            
            logger.info(f"⚖️ Kontrola váhy: {ingredient}, cieľ: {target_weight}g ±{tolerance}g")
            
            # Nastavenie cieľovej hmotnosti pre simuláciu
            if hasattr(self.sensor_manager, 'set_target_weight'):
                self.sensor_manager.set_target_weight(target_weight)
            
            # Spustenie LED efektu pre váženie
            await self.led_controller.play_effect('pulse', duration=10, color='blue')
            
            # Uloženie informácií o kroku
            self.current_step_data = {
                'type': 'weight',
                'ingredient': ingredient,
                'target_weight': target_weight,
                'tolerance': tolerance,
                'message_id': message.message_id
            }
            self.step_start_time = time.time()
            
            # Nastavenie timeoutu
            self.step_timeout_task = asyncio.create_task(
                self.step_timeout(self.config.game.STEP_TIMEOUT)
            )
            
            logger.info("⚖️ Čakám na váženie...")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri kontrole váhy: {e}")
            await self.send_error_response(message, str(e))

    async def handle_start_mixing(self, message: BLEMessage):
        """Spracovanie požiadavky na miešanie"""
        try:
            data = message.data
            direction = data.get('direction', 'clockwise')
            rotations = data.get('rotations', 5)
            speed = data.get('speed', 'medium')
            
            logger.info(f"🥄 Spúšťam miešanie: {direction}, {rotations}x, {speed}")
            
            # Spustenie LED efektu pre miešanie
            await self.led_controller.play_effect(
                'mixing_active', 
                duration=30, 
                direction=direction, 
                speed=speed
            )
            
            # Spustenie simulácie miešania
            if hasattr(self.sensor_manager, 'start_mixing_simulation'):
                self.sensor_manager.start_mixing_simulation()
            
            # Uloženie informácií o kroku
            self.current_step_data = {
                'type': 'mixing',
                'direction': direction,
                'rotations': rotations,
                'speed': speed,
                'current_rotations': 0,
                'message_id': message.message_id
            }
            self.step_start_time = time.time()
            
            # Nastavenie timeoutu
            self.step_timeout_task = asyncio.create_task(
                self.step_timeout(self.config.game.STEP_TIMEOUT)
            )
            
            # Simulácia postupného miešania
            asyncio.create_task(self.simulate_mixing_progress())
            
            logger.info("🥄 Miešanie spustené")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri spustení miešania: {e}")
            await self.send_error_response(message, str(e))

    async def handle_activate_effect(self, message: BLEMessage):
        """Spracovanie požiadavky na aktiváciu efektu"""
        try:
            data = message.data
            effect_type = data.get('type', 'fog')
            duration = data.get('duration', 3)
            led_color = data.get('led_color', '#FFD700')
            
            logger.info(f"✨ Aktivujem efekt: {effect_type}, {duration}s")
            
            # Spustenie LED efektu
            if effect_type == 'fog':
                await self.led_controller.play_effect('sparkle', duration=duration, color='purple')
                # Aktivácia mlhovej trysky (simulácia)
                await self.activate_fog_machine(duration)
            
            # Odpoveď po dokončení efektu
            asyncio.create_task(self.send_effect_complete_response(message, duration))
            
        except Exception as e:
            logger.error(f"❌ Chyba pri aktivácii efektu: {e}")
            await self.send_error_response(message, str(e))

    async def handle_ping(self, message: BLEMessage):
        """Spracovanie ping správy"""
        response = BLEMessage(
            action='pong',
            data={'timestamp': time.time()},
            message_id=self.generate_message_id(),
            response_to_id=message.message_id
        )
        
        await self.send_response(response)

    async def handle_weight_reading(self, reading: SensorReading):
        """Spracovanie čítania váhy"""
        try:
            if not self.current_step_data or self.current_step_data['type'] != 'weight':
                return
            
            weight = reading.value
            target_weight = self.current_step_data['target_weight']
            tolerance = self.current_step_data['tolerance']
            
            # Kontrola presnosti
            is_correct = abs(weight - target_weight) <= tolerance
            
            logger.info(f"⚖️ Váha: {weight}g, cieľ: {target_weight}g, správne: {is_correct}")
            
            # Odoslanie výsledku
            response = BLEMessage(
                action='weight_result',
                data={
                    'current_weight': weight,
                    'target_weight': target_weight,
                    'tolerance': tolerance,
                    'is_correct': is_correct
                },
                message_id=self.generate_message_id(),
                response_to_id=self.current_step_data['message_id']
            )
            
            await self.send_response(response)
            
            if is_correct:
                # Úspešný krok
                await self.led_controller.play_effect('ingredient_correct', duration=2)
                await self.complete_current_step(True)
            else:
                # Neúspešný pokus
                await self.led_controller.play_effect('ingredient_wrong', duration=1)
            
        except Exception as e:
            logger.error(f"❌ Chyba pri spracovaní váhy: {e}")

    async def handle_nfc_reading(self, reading: SensorReading):
        """Spracovanie čítania NFC"""
        try:
            nfc_id = reading.value
            logger.info(f"📡 NFC detekované: {nfc_id}")
            
            # Odoslanie informácie o NFC
            response = BLEMessage(
                action='sensor_reading',
                data={
                    'nfc_detected': True,
                    'nfc_id': nfc_id
                },
                message_id=self.generate_message_id()
            )
            
            await self.send_response(response)
            
        except Exception as e:
            logger.error(f"❌ Chyba pri spracovaní NFC: {e}")

    async def handle_accelerometer_reading(self, reading: SensorReading):
        """Spracovanie čítania akcelerometra"""
        try:
            if not self.current_step_data or self.current_step_data['type'] != 'mixing':
                return
            
            if reading.raw_value and reading.raw_value.get('mixing'):
                # Detekované miešanie
                self.current_step_data['current_rotations'] += 1
                
                current_rotations = self.current_step_data['current_rotations']
                target_rotations = self.current_step_data['rotations']
                
                logger.info(f"🥄 Miešanie: {current_rotations}/{target_rotations}")
                
                # Odoslanie pokroku
                response = BLEMessage(
                    action='mixing_progress',
                    data={
                        'current_rotations': current_rotations,
                        'target_rotations': target_rotations,
                        'direction': self.current_step_data['direction'],
                        'is_complete': current_rotations >= target_rotations
                    },
                    message_id=self.generate_message_id(),
                    response_to_id=self.current_step_data['message_id']
                )
                
                await self.send_response(response)
                
                if current_rotations >= target_rotations:
                    # Miešanie dokončené
                    await self.led_controller.play_effect('mixing_complete', duration=2)
                    await self.complete_current_step(True)
                    
                    # Zastavenie simulácie miešania
                    if hasattr(self.sensor_manager, 'stop_mixing_simulation'):
                        self.sensor_manager.stop_mixing_simulation()
            
        except Exception as e:
            logger.error(f"❌ Chyba pri spracovaní akcelerometra: {e}")

    async def simulate_mixing_progress(self):
        """Simulácia pokroku miešania"""
        try:
            await asyncio.sleep(2)  # Krátka pauza pred začiatkom
            
            while (self.current_step_data and 
                   self.current_step_data['type'] == 'mixing' and
                   self.current_step_data['current_rotations'] < self.current_step_data['rotations']):
                
                # Simulácia detekcie rotácie
                await self.handle_accelerometer_reading(SensorReading(
                    sensor_type='accelerometer',
                    value={'x': 0.5, 'y': 0.5, 'z': 0.1},
                    timestamp=time.time(),
                    raw_value={'mixing': True}
                ))
                
                await asyncio.sleep(2)  # 2 sekundy na rotáciu
                
        except Exception as e:
            logger.error(f"❌ Chyba pri simulácii miešania: {e}")

    async def activate_fog_machine(self, duration: float):
        """Aktivácia mlhovej trysky"""
        try:
            logger.info(f"💨 Aktivujem mlhovú trysku na {duration}s")
            
            # V reálnej implementácii by sa tu zapol GPIO pin pre relé
            # GPIO.output(self.config.FOG_RELAY_PIN, GPIO.HIGH)
            
            await asyncio.sleep(duration)
            
            # GPIO.output(self.config.FOG_RELAY_PIN, GPIO.LOW)
            
            logger.info("💨 Mlhová tryska vypnutá")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri aktivácii mlhovej trysky: {e}")

    async def send_effect_complete_response(self, original_message: BLEMessage, duration: float):
        """Odoslanie odpovede po dokončení efektu"""
        try:
            await asyncio.sleep(duration)
            
            response = BLEMessage(
                action='effect_complete',
                data={
                    'type': original_message.data.get('type'),
                    'duration': duration,
                    'success': True
                },
                message_id=self.generate_message_id(),
                response_to_id=original_message.message_id
            )
            
            await self.send_response(response)
            
        except Exception as e:
            logger.error(f"❌ Chyba pri odosielaní effect_complete: {e}")

    async def complete_current_step(self, success: bool):
        """Dokončenie aktuálneho kroku"""
        try:
            if self.step_timeout_task:
                self.step_timeout_task.cancel()
                self.step_timeout_task = None
            
            step_time = time.time() - self.step_start_time if self.step_start_time else 0
            
            logger.info(f"✅ Krok dokončený: úspech={success}, čas={step_time:.1f}s")
            
            # Vymazanie aktuálneho kroku
            self.current_step_data = None
            self.step_start_time = None
            
            # Návrat do čakacieho režimu
            await self.led_controller.play_effect('waiting', duration=float('inf'))
            
        except Exception as e:
            logger.error(f"❌ Chyba pri dokončovaní kroku: {e}")

    async def step_timeout(self, timeout_seconds: float):
        """Timeout pre krok"""
        try:
            await asyncio.sleep(timeout_seconds)
            
            logger.warning(f"⏰ Timeout kroku po {timeout_seconds}s")
            
            # Odoslanie timeout správy
            if self.current_step_data:
                response = BLEMessage(
                    action='step_timeout',
                    data={
                        'step_type': self.current_step_data['type'],
                        'timeout_seconds': timeout_seconds
                    },
                    message_id=self.generate_message_id()
                )
                
                await self.send_response(response)
            
            # Vyčistenie kroku
            await self.complete_current_step(False)
            
        except asyncio.CancelledError:
            # Timeout bol zrušený (krok dokončený včas)
            pass
        except Exception as e:
            logger.error(f"❌ Chyba pri step timeout: {e}")

    async def process_events(self):
        """Spracovanie fronty udalostí"""
        try:
            while self.event_queue:
                event = self.event_queue.pop(0)
                await self.process_event(event)
                
        except Exception as e:
            logger.error(f"❌ Chyba pri spracovaní udalostí: {e}")

    async def process_event(self, event: Dict[str, Any]):
        """Spracovanie jednej udalosti"""
        try:
            event_type = event.get('type')
            
            if event_type == 'game_start':
                await self.start_game_session(event.get('data', {}))
            elif event_type == 'game_end':
                await self.end_game_session(event.get('data', {}))
            else:
                logger.warning(f"⚠️ Neznámy typ udalosti: {event_type}")
                
        except Exception as e:
            logger.error(f"❌ Chyba pri spracovaní udalosti: {e}")

    async def start_game_session(self, data: Dict[str, Any]):
        """Spustenie hernej session"""
        try:
            session_id = data.get('session_id', f"session_{int(time.time())}")
            
            self.current_session = GameSession(
                session_id=session_id,
                start_time=time.time(),
                state=GameState.GAME_ACTIVE
            )
            
            self.game_state = GameState.GAME_ACTIVE
            
            logger.info(f"🎮 Herná session spustená: {session_id}")
            
        except Exception as e:
            logger.error(f"❌ Chyba pri spustení hernej session: {e}")

    async def end_game_session(self, data: Dict[str, Any]):
        """Ukončenie hernej session"""
        try:
            if self.current_session:
                self.current_session.end_time = time.time()
                self.current_session.state = GameState.GAME_COMPLETED
                
                logger.info(f"🏁 Herná session ukončená: {self.current_session.session_id}")
                
                self.current_session = None
            
            self.game_state = GameState.CONNECTED
            
        except Exception as e:
            logger.error(f"❌ Chyba pri ukončení hernej session: {e}")

    async def send_response(self, message: BLEMessage):
        """Odoslanie odpovede"""
        if self.response_callback:
            await self.response_callback(message)
        else:
            logger.warning("⚠️ Response callback nie je nastavený")

    async def send_error_response(self, original_message: BLEMessage, error_message: str):
        """Odoslanie chybovej odpovede"""
        response = BLEMessage(
            action='error',
            data={'message': error_message},
            message_id=self.generate_message_id(),
            response_to_id=original_message.message_id if original_message else None
        )
        
        await self.send_response(response)

    def generate_message_id(self) -> str:
        """Generovanie ID správy"""
        return f"rpi_{int(time.time() * 1000)}"

    def get_status(self) -> Dict[str, Any]:
        """Získanie stavu hernej logiky"""
        return {
            'game_state': self.game_state.value,
            'current_session': self.current_session.session_id if self.current_session else None,
            'current_step': self.current_step_data['type'] if self.current_step_data else None,
            'events_in_queue': len(self.event_queue)
        }
