<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.alchemy.game">

    <!-- Bluetooth permissions -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    
    <!-- Camera permission for QR scanner -->
    <uses-permission android:name="android.permission.CAMERA" />
    
    <!-- Audio permissions -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    
    <!-- Bluetooth LE permissions for newer Android versions -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    
    <!-- Hardware features -->
    <uses-feature android:name="android.hardware.bluetooth" android:required="true" />
    <uses-feature android:name="android.hardware.camera" android:required="true" />
    <uses-feature android:name="android.hardware.camera.autofocus" android:required="false" />

    <application
        android:allowBackup="true"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:theme="@style/AppTheme"
        android:screenOrientation="landscape"
        android:configChanges="orientation|keyboardHidden|screenSize">
        
        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:screenOrientation="landscape"
            android:theme="@style/SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <!-- Game Activity -->
        <activity
            android:name=".GameActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:theme="@style/GameTheme" />
        
        <!-- QR Scanner Activity -->
        <activity
            android:name=".QRScannerActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:theme="@style/ScannerTheme" />
        
        <!-- Results Activity -->
        <activity
            android:name=".ResultsActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:theme="@style/GameTheme" />
            
    </application>
</manifest>
