package com.alchemy;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * Model trieda pre recept elixíru
 * Obsahuje všetky informácie potrebné pre herný proces
 */
public class Recipe {
    
    @SerializedName("id")
    private int id;
    
    @SerializedName("name")
    private String name;
    
    @SerializedName("difficulty")
    private String difficulty;
    
    @SerializedName("time_limit")
    private int timeLimit; // v sekund<PERSON>ch
    
    @SerializedName("description")
    private String description;
    
    @SerializedName("steps")
    private List<RecipeStep> steps;
    
    @SerializedName("success_message")
    private String successMessage;
    
    @SerializedName("failure_message")
    private String failureMessage;
    
    // Konštruktory
    public Recipe() {}
    
    public Recipe(int id, String name, String difficulty, int timeLimit, 
                  String description, List<RecipeStep> steps) {
        this.id = id;
        this.name = name;
        this.difficulty = difficulty;
        this.timeLimit = timeLimit;
        this.description = description;
        this.steps = steps;
    }
    
    // Getters a Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDifficulty() {
        return difficulty;
    }
    
    public void setDifficulty(String difficulty) {
        this.difficulty = difficulty;
    }
    
    public int getTimeLimit() {
        return timeLimit;
    }
    
    public void setTimeLimit(int timeLimit) {
        this.timeLimit = timeLimit;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public List<RecipeStep> getSteps() {
        return steps;
    }
    
    public void setSteps(List<RecipeStep> steps) {
        this.steps = steps;
    }
    
    public String getSuccessMessage() {
        return successMessage;
    }
    
    public void setSuccessMessage(String successMessage) {
        this.successMessage = successMessage;
    }
    
    public String getFailureMessage() {
        return failureMessage;
    }
    
    public void setFailureMessage(String failureMessage) {
        this.failureMessage = failureMessage;
    }
    
    /**
     * Získanie počtu krokov receptu
     */
    public int getStepCount() {
        return steps != null ? steps.size() : 0;
    }
    
    /**
     * Získanie konkrétneho kroku podľa indexu
     */
    public RecipeStep getStep(int index) {
        if (steps != null && index >= 0 && index < steps.size()) {
            return steps.get(index);
        }
        return null;
    }
    
    /**
     * Kontrola či je recept platný
     */
    public boolean isValid() {
        return id > 0 && 
               name != null && !name.trim().isEmpty() &&
               difficulty != null && !difficulty.trim().isEmpty() &&
               timeLimit > 0 &&
               steps != null && !steps.isEmpty();
    }
    
    /**
     * Získanie obtiažnosti ako enum
     */
    public Difficulty getDifficultyLevel() {
        if (difficulty == null) {
            return Difficulty.EASY;
        }
        
        switch (difficulty.toLowerCase()) {
            case "easy":
                return Difficulty.EASY;
            case "medium":
                return Difficulty.MEDIUM;
            case "hard":
                return Difficulty.HARD;
            default:
                return Difficulty.EASY;
        }
    }
    
    /**
     * Enum pre úrovne obtiažnosti
     */
    public enum Difficulty {
        EASY("easy", "Ľahká", 1.0f),
        MEDIUM("medium", "Stredná", 1.2f),
        HARD("hard", "Ťažká", 1.5f);
        
        private final String code;
        private final String displayName;
        private final float scoreMultiplier;
        
        Difficulty(String code, String displayName, float scoreMultiplier) {
            this.code = code;
            this.displayName = displayName;
            this.scoreMultiplier = scoreMultiplier;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public float getScoreMultiplier() {
            return scoreMultiplier;
        }
    }
    
    @Override
    public String toString() {
        return "Recipe{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", difficulty='" + difficulty + '\'' +
                ", timeLimit=" + timeLimit +
                ", stepCount=" + getStepCount() +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        Recipe recipe = (Recipe) obj;
        return id == recipe.id;
    }
    
    @Override
    public int hashCode() {
        return Integer.hashCode(id);
    }
}
