package com.alchemy;

import android.Manifest;
import android.bluetooth.BluetoothAdapter;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.VideoView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.alchemy.game.R;

/**
 * Hlavná aktivita aplikácie - Splash screen a úvodné menu
 * Obsahuje úvodnú animáciu s Merlinom a hlavné menu
 */
public class MainActivity extends AppCompatActivity {
    
    private static final int REQUEST_ENABLE_BT = 1;
    private static final int REQUEST_PERMISSIONS = 2;
    private static final int SPLASH_DURATION = 3000; // 3 sekundy
    
    private VideoView introVideo;
    private View splashLayout;
    private View menuLayout;
    private Button startGameButton;
    private Button scanRecipeButton;
    private Button settingsButton;
    private Button exitButton;
    private TextView welcomeText;
    
    private BluetoothManager bluetoothManager;
    private MediaPlayer backgroundMusic;
    private boolean isIntroPlaying = false;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Nastavenie fullscreen režimu
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        
        setContentView(R.layout.activity_main);
        
        initializeViews();
        checkPermissions();
        initializeBluetooth();
        showSplashScreen();
    }
    
    /**
     * Inicializácia všetkých view komponentov
     */
    private void initializeViews() {
        splashLayout = findViewById(R.id.splash_layout);
        menuLayout = findViewById(R.id.menu_layout);
        introVideo = findViewById(R.id.intro_video);
        startGameButton = findViewById(R.id.btn_start_game);
        scanRecipeButton = findViewById(R.id.btn_scan_recipe);
        settingsButton = findViewById(R.id.btn_settings);
        exitButton = findViewById(R.id.btn_exit);
        welcomeText = findViewById(R.id.welcome_text);
        
        // Nastavenie click listenerov
        startGameButton.setOnClickListener(v -> startGame());
        scanRecipeButton.setOnClickListener(v -> scanRecipe());
        settingsButton.setOnClickListener(v -> openSettings());
        exitButton.setOnClickListener(v -> exitApp());
    }
    
    /**
     * Kontrola a požiadanie o potrebné povolenia
     */
    private void checkPermissions() {
        String[] permissions = {
            Manifest.permission.BLUETOOTH,
            Manifest.permission.BLUETOOTH_ADMIN,
            Manifest.permission.ACCESS_COARSE_LOCATION,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.CAMERA,
            Manifest.permission.BLUETOOTH_SCAN,
            Manifest.permission.BLUETOOTH_CONNECT
        };
        
        boolean allPermissionsGranted = true;
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(this, permission) 
                != PackageManager.PERMISSION_GRANTED) {
                allPermissionsGranted = false;
                break;
            }
        }
        
        if (!allPermissionsGranted) {
            ActivityCompat.requestPermissions(this, permissions, REQUEST_PERMISSIONS);
        }
    }
    
    /**
     * Inicializácia Bluetooth managera
     */
    private void initializeBluetooth() {
        bluetoothManager = new BluetoothManager(this);
        
        if (!bluetoothManager.isBluetoothAvailable()) {
            Toast.makeText(this, R.string.bluetooth_not_available, Toast.LENGTH_LONG).show();
            return;
        }
        
        if (!bluetoothManager.isBluetoothEnabled()) {
            Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
            startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT);
        }
    }
    
    /**
     * Zobrazenie splash screen s logom
     */
    private void showSplashScreen() {
        splashLayout.setVisibility(View.VISIBLE);
        menuLayout.setVisibility(View.GONE);
        
        // Po 3 sekundách prejsť na úvodné video
        new Handler().postDelayed(() -> {
            showIntroVideo();
        }, SPLASH_DURATION);
    }
    
    /**
     * Prehranie úvodného videa s Merlinom
     */
    private void showIntroVideo() {
        if (introVideo != null) {
            isIntroPlaying = true;
            // Nastavenie videa z raw resources
            String videoPath = "android.resource://" + getPackageName() + "/" + R.raw.merlin_intro;
            introVideo.setVideoPath(videoPath);
            
            introVideo.setOnCompletionListener(mp -> {
                isIntroPlaying = false;
                showMainMenu();
            });
            
            introVideo.setOnErrorListener((mp, what, extra) -> {
                isIntroPlaying = false;
                showMainMenu();
                return true;
            });
            
            introVideo.start();
        } else {
            showMainMenu();
        }
    }
    
    /**
     * Zobrazenie hlavného menu
     */
    private void showMainMenu() {
        splashLayout.setVisibility(View.GONE);
        menuLayout.setVisibility(View.VISIBLE);
        
        startBackgroundMusic();
        
        // Animácia pre buttony
        animateMenuButtons();
    }
    
    /**
     * Animácia pre menu buttony
     */
    private void animateMenuButtons() {
        Button[] buttons = {startGameButton, scanRecipeButton, settingsButton, exitButton};
        
        for (int i = 0; i < buttons.length; i++) {
            Button button = buttons[i];
            button.setAlpha(0f);
            button.animate()
                .alpha(1f)
                .setDuration(500)
                .setStartDelay(i * 200)
                .start();
        }
    }
    
    /**
     * Spustenie pozadovej hudby
     */
    private void startBackgroundMusic() {
        try {
            if (backgroundMusic == null) {
                backgroundMusic = MediaPlayer.create(this, R.raw.magic_background);
                backgroundMusic.setLooping(true);
                backgroundMusic.setVolume(0.3f, 0.3f);
            }
            
            if (!backgroundMusic.isPlaying()) {
                backgroundMusic.start();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * Spustenie hry - prechod na QR scanner alebo priamo na hru
     */
    private void startGame() {
        if (!bluetoothManager.isConnected()) {
            // Pokus o pripojenie k Raspberry Pi
            bluetoothManager.connectToDevice();
            
            // Zobrazenie loading dialógu
            Toast.makeText(this, "Pripájam sa k čarodejníckej dielni...", Toast.LENGTH_SHORT).show();
            
            // Po 3 sekundách skontrolovať pripojenie
            new Handler().postDelayed(() -> {
                if (bluetoothManager.isConnected()) {
                    openQRScanner();
                } else {
                    Toast.makeText(this, R.string.connection_failed, Toast.LENGTH_LONG).show();
                }
            }, 3000);
        } else {
            openQRScanner();
        }
    }
    
    /**
     * Otvorenie QR scannera
     */
    private void scanRecipe() {
        openQRScanner();
    }
    
    /**
     * Otvorenie QR scanner aktivity
     */
    private void openQRScanner() {
        Intent intent = new Intent(this, QRScannerActivity.class);
        startActivity(intent);
    }
    
    /**
     * Otvorenie nastavení
     */
    private void openSettings() {
        // TODO: Implementovať settings aktivitu
        Toast.makeText(this, "Nastavenia - zatiaľ nedostupné", Toast.LENGTH_SHORT).show();
    }
    
    /**
     * Ukončenie aplikácie
     */
    private void exitApp() {
        if (backgroundMusic != null && backgroundMusic.isPlaying()) {
            backgroundMusic.stop();
            backgroundMusic.release();
        }
        
        if (bluetoothManager != null) {
            bluetoothManager.disconnect();
        }
        
        finish();
        System.exit(0);
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        if (backgroundMusic != null && !isIntroPlaying) {
            startBackgroundMusic();
        }
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        if (backgroundMusic != null && backgroundMusic.isPlaying()) {
            backgroundMusic.pause();
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (backgroundMusic != null) {
            backgroundMusic.release();
            backgroundMusic = null;
        }
        
        if (bluetoothManager != null) {
            bluetoothManager.disconnect();
        }
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == REQUEST_ENABLE_BT) {
            if (resultCode == RESULT_OK) {
                Toast.makeText(this, "Bluetooth zapnutý", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "Bluetooth je potrebný pre hru", Toast.LENGTH_LONG).show();
            }
        }
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == REQUEST_PERMISSIONS) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            
            if (!allGranted) {
                Toast.makeText(this, "Všetky povolenia sú potrebné pre správne fungovanie hry", 
                    Toast.LENGTH_LONG).show();
            }
        }
    }
    
    @Override
    public void onBackPressed() {
        if (isIntroPlaying) {
            // Preskočenie intro videa
            if (introVideo != null && introVideo.isPlaying()) {
                introVideo.stopPlayback();
            }
            showMainMenu();
        } else {
            super.onBackPressed();
        }
    }
}
