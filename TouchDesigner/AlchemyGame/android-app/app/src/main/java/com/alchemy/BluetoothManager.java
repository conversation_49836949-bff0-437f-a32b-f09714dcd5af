package com.alchemy;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.JsonObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <PERSON><PERSON><PERSON><PERSON>r pre Bluetooth komunikáciu s Raspberry Pi
 * Zabezpečuje pripojenie, odosielanie a prijímanie JSON správ
 */
public class BluetoothManager {
    
    private static final String TAG = "BluetoothManager";
    private static final String RASPBERRY_PI_NAME = "AlchemyPi";
    private static final String RASPBERRY_PI_MAC = "B8:27:EB:XX:XX:XX"; // Nahradiť skutočnou MAC adresou
    private static final UUID SERVICE_UUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB");
    private static final int CONNECTION_TIMEOUT = 5000; // 5 sekúnd
    
    private Context context;
    private BluetoothAdapter bluetoothAdapter;
    private BluetoothSocket socket;
    private BluetoothDevice raspberryPi;
    private PrintWriter outputWriter;
    private BufferedReader inputReader;
    private ExecutorService executorService;
    private Handler mainHandler;
    private Gson gson;
    
    private boolean isConnected = false;
    private boolean isConnecting = false;
    
    // Callback interface pre komunikáciu s aktivitami
    public interface BluetoothCallback {
        void onConnected();
        void onDisconnected();
        void onMessageReceived(JsonObject message);
        void onError(String error);
    }
    
    private BluetoothCallback callback;
    
    public BluetoothManager(Context context) {
        this.context = context;
        this.bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        this.executorService = Executors.newSingleThreadExecutor();
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.gson = new Gson();
    }
    
    /**
     * Kontrola dostupnosti Bluetooth
     */
    public boolean isBluetoothAvailable() {
        return bluetoothAdapter != null;
    }
    
    /**
     * Kontrola či je Bluetooth zapnutý
     */
    public boolean isBluetoothEnabled() {
        return bluetoothAdapter != null && bluetoothAdapter.isEnabled();
    }
    
    /**
     * Kontrola pripojenia
     */
    public boolean isConnected() {
        return isConnected && socket != null && socket.isConnected();
    }
    
    /**
     * Nastavenie callback pre komunikáciu
     */
    public void setCallback(BluetoothCallback callback) {
        this.callback = callback;
    }
    
    /**
     * Pripojenie k Raspberry Pi zariadeniu
     */
    public void connectToDevice() {
        if (isConnecting || isConnected) {
            Log.w(TAG, "Už prebieha pripojenie alebo je pripojené");
            return;
        }
        
        if (!isBluetoothEnabled()) {
            notifyError("Bluetooth nie je zapnutý");
            return;
        }
        
        isConnecting = true;
        
        executorService.execute(() -> {
            try {
                // Hľadanie Raspberry Pi zariadenia
                findRaspberryPi();
                
                if (raspberryPi == null) {
                    notifyError("Raspberry Pi zariadenie nenájdené");
                    return;
                }
                
                // Vytvorenie socket pripojenia
                socket = raspberryPi.createRfcommSocketToServiceRecord(SERVICE_UUID);
                
                // Pripojenie
                Log.d(TAG, "Pripájam sa k " + raspberryPi.getName());
                socket.connect();
                
                // Nastavenie input/output streamov
                outputWriter = new PrintWriter(new OutputStreamWriter(socket.getOutputStream()), true);
                inputReader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                
                isConnected = true;
                isConnecting = false;
                
                Log.d(TAG, "Pripojenie úspešné");
                notifyConnected();
                
                // Spustenie čítania správ
                startMessageReading();
                
            } catch (IOException e) {
                Log.e(TAG, "Chyba pripojenia: " + e.getMessage());
                isConnecting = false;
                notifyError("Pripojenie zlyhalo: " + e.getMessage());
                cleanup();
            }
        });
    }
    
    /**
     * Hľadanie Raspberry Pi zariadenia v párovaných zariadeniach
     */
    private void findRaspberryPi() {
        if (bluetoothAdapter.getBondedDevices() != null) {
            for (BluetoothDevice device : bluetoothAdapter.getBondedDevices()) {
                if (device.getName() != null && 
                    (device.getName().contains(RASPBERRY_PI_NAME) || 
                     device.getAddress().equals(RASPBERRY_PI_MAC))) {
                    raspberryPi = device;
                    Log.d(TAG, "Našiel som Raspberry Pi: " + device.getName() + " (" + device.getAddress() + ")");
                    break;
                }
            }
        }
    }
    
    /**
     * Spustenie čítania správ v samostatnom vlákne
     */
    private void startMessageReading() {
        executorService.execute(() -> {
            try {
                String message;
                while (isConnected && (message = inputReader.readLine()) != null) {
                    Log.d(TAG, "Prijatá správa: " + message);
                    
                    try {
                        JsonObject jsonMessage = gson.fromJson(message, JsonObject.class);
                        notifyMessageReceived(jsonMessage);
                    } catch (Exception e) {
                        Log.e(TAG, "Chyba parsingu JSON: " + e.getMessage());
                    }
                }
            } catch (IOException e) {
                if (isConnected) {
                    Log.e(TAG, "Chyba čítania správ: " + e.getMessage());
                    notifyError("Spojenie prerušené");
                    disconnect();
                }
            }
        });
    }
    
    /**
     * Odoslanie JSON správy
     */
    public void sendMessage(JsonObject message) {
        if (!isConnected || outputWriter == null) {
            Log.w(TAG, "Nie je pripojené, nemôžem odoslať správu");
            return;
        }
        
        executorService.execute(() -> {
            try {
                String jsonString = gson.toJson(message);
                Log.d(TAG, "Odosielam správu: " + jsonString);
                outputWriter.println(jsonString);
                outputWriter.flush();
            } catch (Exception e) {
                Log.e(TAG, "Chyba odosielania správy: " + e.getMessage());
                notifyError("Chyba odosielania správy");
            }
        });
    }
    
    /**
     * Odoslanie akcie na kontrolu hmotnosti
     */
    public void checkWeight(String ingredient, double targetWeight) {
        JsonObject message = new JsonObject();
        message.addProperty("action", "check_weight");
        message.addProperty("ingredient", ingredient);
        message.addProperty("target", targetWeight);
        sendMessage(message);
    }
    
    /**
     * Odoslanie akcie na spustenie miešania
     */
    public void startMixing(String direction, int count) {
        JsonObject message = new JsonObject();
        message.addProperty("action", "start_mixing");
        message.addProperty("direction", direction);
        message.addProperty("count", count);
        sendMessage(message);
    }
    
    /**
     * Odoslanie akcie na aktiváciu efektu
     */
    public void activateEffect(String type, int duration) {
        JsonObject message = new JsonObject();
        message.addProperty("action", "activate_effect");
        message.addProperty("type", type);
        message.addProperty("duration", duration);
        sendMessage(message);
    }
    
    /**
     * Odpojenie od zariadenia
     */
    public void disconnect() {
        isConnected = false;
        isConnecting = false;
        
        executorService.execute(() -> {
            cleanup();
            notifyDisconnected();
        });
    }
    
    /**
     * Vyčistenie zdrojov
     */
    private void cleanup() {
        try {
            if (outputWriter != null) {
                outputWriter.close();
                outputWriter = null;
            }
            
            if (inputReader != null) {
                inputReader.close();
                inputReader = null;
            }
            
            if (socket != null) {
                socket.close();
                socket = null;
            }
        } catch (IOException e) {
            Log.e(TAG, "Chyba pri zatváraní spojenia: " + e.getMessage());
        }
    }
    
    /**
     * Notifikácia o úspešnom pripojení
     */
    private void notifyConnected() {
        mainHandler.post(() -> {
            if (callback != null) {
                callback.onConnected();
            }
        });
    }
    
    /**
     * Notifikácia o odpojení
     */
    private void notifyDisconnected() {
        mainHandler.post(() -> {
            if (callback != null) {
                callback.onDisconnected();
            }
        });
    }
    
    /**
     * Notifikácia o prijatej správe
     */
    private void notifyMessageReceived(JsonObject message) {
        mainHandler.post(() -> {
            if (callback != null) {
                callback.onMessageReceived(message);
            }
        });
    }
    
    /**
     * Notifikácia o chybe
     */
    private void notifyError(String error) {
        mainHandler.post(() -> {
            if (callback != null) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Ukončenie BluetoothManagera
     */
    public void shutdown() {
        disconnect();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
