package com.alchemy;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.alchemy.game.R;
import com.google.zxing.ResultPoint;
import com.journeyapps.barcodescanner.BarcodeCallback;
import com.journeyapps.barcodescanner.BarcodeResult;
import com.journeyapps.barcodescanner.DecoratedBarcodeView;

import java.util.List;

/**
 * Aktivita pre skenovanie QR kódov receptov
 * Používa ZXing knižnicu pre skenovanie QR kódov
 */
public class QRScannerActivity extends AppCompatActivity {
    
    private static final String TAG = "QRScannerActivity";
    
    private DecoratedBarcodeView barcodeView;
    private TextView instructionText;
    private Button cancelButton;
    private DatabaseHelper databaseHelper;
    
    private boolean isScanning = true;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_qr_scanner);
        
        initializeViews();
        initializeDatabase();
        setupScanner();
    }
    
    /**
     * Inicializácia view komponentov
     */
    private void initializeViews() {
        barcodeView = findViewById(R.id.barcode_view);
        instructionText = findViewById(R.id.instruction_text);
        cancelButton = findViewById(R.id.btn_cancel);
        
        cancelButton.setOnClickListener(v -> finish());
        
        // Nastavenie inštrukčného textu
        instructionText.setText(R.string.scan_qr_instruction);
    }
    
    /**
     * Inicializácia databázy
     */
    private void initializeDatabase() {
        databaseHelper = new DatabaseHelper(this);
    }
    
    /**
     * Nastavenie QR code scannera
     */
    private void setupScanner() {
        barcodeView.decodeContinuous(new BarcodeCallback() {
            @Override
            public void barcodeResult(BarcodeResult result) {
                if (!isScanning) {
                    return;
                }
                
                isScanning = false;
                handleQRCodeResult(result.getText());
            }
            
            @Override
            public void possibleResultPoints(List<ResultPoint> resultPoints) {
                // Môžeme tu pridať vizuálne efekty pre detekované body
            }
        });
        
        // Nastavenie vlastností scannera
        barcodeView.setStatusText("");
        barcodeView.getBarcodeView().setDecoderFactory(new DefaultDecoderFactory());
        barcodeView.initializeFromIntent(getIntent());
    }
    
    /**
     * Spracovanie výsledku QR kódu
     */
    private void handleQRCodeResult(String qrContent) {
        Log.d(TAG, "QR kód naskenovaný: " + qrContent);
        
        try {
            // Očakávame QR kód vo formáte: "recipe_id:1" alebo len "1"
            int recipeId = parseRecipeId(qrContent);
            
            if (recipeId > 0) {
                // Kontrola či recept existuje v databáze
                Recipe recipe = databaseHelper.getRecipe(recipeId);
                
                if (recipe != null) {
                    // Úspešne naskenovaný platný recept
                    Toast.makeText(this, "Recept načítaný: " + recipe.getName(), 
                        Toast.LENGTH_SHORT).show();
                    
                    // Spustenie hry s vybraným receptom
                    startGameWithRecipe(recipe);
                } else {
                    // Recept nenájdený v databáze
                    showError(getString(R.string.recipe_not_found));
                }
            } else {
                // Neplatný formát QR kódu
                showError(getString(R.string.qr_scan_failed));
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Chyba spracovania QR kódu: " + e.getMessage());
            showError(getString(R.string.qr_scan_failed));
        }
    }
    
    /**
     * Parsovanie ID receptu z QR kódu
     */
    private int parseRecipeId(String qrContent) {
        try {
            // Ak QR kód obsahuje "recipe_id:", extrahujeme číslo
            if (qrContent.startsWith("recipe_id:")) {
                return Integer.parseInt(qrContent.substring(10));
            }
            
            // Ak je QR kód len číslo
            return Integer.parseInt(qrContent.trim());
            
        } catch (NumberFormatException e) {
            Log.e(TAG, "Neplatný formát QR kódu: " + qrContent);
            return -1;
        }
    }
    
    /**
     * Spustenie hry s vybraným receptom
     */
    private void startGameWithRecipe(Recipe recipe) {
        Intent gameIntent = new Intent(this, GameActivity.class);
        gameIntent.putExtra("recipe_id", recipe.getId());
        gameIntent.putExtra("recipe_name", recipe.getName());
        gameIntent.putExtra("recipe_difficulty", recipe.getDifficulty());
        gameIntent.putExtra("time_limit", recipe.getTimeLimit());
        
        startActivity(gameIntent);
        finish(); // Zatvorenie scanner aktivity
    }
    
    /**
     * Zobrazenie chybovej správy a možnosť opakovať skenovanie
     */
    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
        
        // Povoliť opätovné skenovanie po 2 sekundách
        barcodeView.postDelayed(() -> {
            isScanning = true;
            barcodeView.resume();
        }, 2000);
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        if (barcodeView != null) {
            barcodeView.resume();
        }
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        if (barcodeView != null) {
            barcodeView.pause();
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (databaseHelper != null) {
            databaseHelper.close();
        }
    }
    
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        return barcodeView.onKeyDown(keyCode, event) || super.onKeyDown(keyCode, event);
    }
    
    /**
     * Defaultný decoder factory pre QR kódy
     */
    private static class DefaultDecoderFactory implements com.journeyapps.barcodescanner.DecoderFactory {
        @Override
        public com.google.zxing.Decoder createDecoder(java.util.Map<com.google.zxing.DecodeHintType, ?> hints) {
            return new com.google.zxing.MultiFormatReader();
        }
    }
}
