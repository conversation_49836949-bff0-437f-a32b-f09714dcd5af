package com.alchemy;

import com.google.gson.annotations.SerializedName;

/**
 * Model trieda pre jeden krok receptu
 * <PERSON><PERSON><PERSON><PERSON> byť typu v<PERSON>, miešanie alebo efekt
 */
public class RecipeStep {
    
    @SerializedName("type")
    private String type;
    
    @SerializedName("instruction")
    private String instruction;
    
    @SerializedName("ingredient")
    private String ingredient;
    
    @SerializedName("amount")
    private double amount;
    
    @SerializedName("tolerance")
    private double tolerance;
    
    @SerializedName("direction")
    private String direction;
    
    @SerializedName("rotations")
    private int rotations;
    
    @SerializedName("duration")
    private int duration;
    
    @SerializedName("effect_type")
    private String effectType;
    
    @SerializedName("points")
    private int points;
    
    // Konštruktory
    public RecipeStep() {}
    
    public RecipeStep(String type, String instruction) {
        this.type = type;
        this.instruction = instruction;
    }
    
    // Getters a Setters
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getInstruction() {
        return instruction;
    }
    
    public void setInstruction(String instruction) {
        this.instruction = instruction;
    }
    
    public String getIngredient() {
        return ingredient;
    }
    
    public void setIngredient(String ingredient) {
        this.ingredient = ingredient;
    }
    
    public double getAmount() {
        return amount;
    }
    
    public void setAmount(double amount) {
        this.amount = amount;
    }
    
    public double getTolerance() {
        return tolerance;
    }
    
    public void setTolerance(double tolerance) {
        this.tolerance = tolerance;
    }
    
    public String getDirection() {
        return direction;
    }
    
    public void setDirection(String direction) {
        this.direction = direction;
    }
    
    public int getRotations() {
        return rotations;
    }
    
    public void setRotations(int rotations) {
        this.rotations = rotations;
    }
    
    public int getDuration() {
        return duration;
    }
    
    public void setDuration(int duration) {
        this.duration = duration;
    }
    
    public String getEffectType() {
        return effectType;
    }
    
    public void setEffectType(String effectType) {
        this.effectType = effectType;
    }
    
    public int getPoints() {
        return points;
    }
    
    public void setPoints(int points) {
        this.points = points;
    }
    
    /**
     * Získanie typu kroku ako enum
     */
    public StepType getStepType() {
        if (type == null) {
            return StepType.UNKNOWN;
        }
        
        switch (type.toLowerCase()) {
            case "weigh":
                return StepType.WEIGH;
            case "mix":
                return StepType.MIX;
            case "effect":
                return StepType.EFFECT;
            case "wait":
                return StepType.WAIT;
            default:
                return StepType.UNKNOWN;
        }
    }
    
    /**
     * Kontrola či je hmotnosť v tolerancii
     */
    public boolean isWeightInTolerance(double currentWeight) {
        if (getStepType() != StepType.WEIGH) {
            return false;
        }
        
        double minWeight = amount - tolerance;
        double maxWeight = amount + tolerance;
        
        return currentWeight >= minWeight && currentWeight <= maxWeight;
    }
    
    /**
     * Získanie smerového textu pre miešanie
     */
    public String getDirectionText() {
        if ("clockwise".equals(direction)) {
            return "doprava";
        } else if ("counterclockwise".equals(direction)) {
            return "doľava";
        }
        return direction;
    }
    
    /**
     * Získanie názvu ingrediencie v slovenčine
     */
    public String getIngredientDisplayName() {
        if (ingredient == null) {
            return "";
        }
        
        switch (ingredient) {
            case "dragons_blood":
                return "Dračia Krv";
            case "sea_foam":
                return "Morská Pena";
            case "sun_dust":
                return "Slnečný Prach";
            case "fairy_herb":
                return "Vílinský Múdrik";
            case "bat_tooth":
                return "Netopierí Zub";
            case "midnight_dew":
                return "Polnočná Rosa";
            default:
                return ingredient;
        }
    }
    
    /**
     * Kontrola platnosti kroku
     */
    public boolean isValid() {
        if (type == null || instruction == null) {
            return false;
        }
        
        StepType stepType = getStepType();
        
        switch (stepType) {
            case WEIGH:
                return ingredient != null && amount > 0 && tolerance >= 0;
            case MIX:
                return direction != null && rotations > 0;
            case EFFECT:
                return effectType != null && duration > 0;
            case WAIT:
                return duration > 0;
            default:
                return false;
        }
    }
    
    /**
     * Enum pre typy krokov
     */
    public enum StepType {
        WEIGH("weigh", "Váženie"),
        MIX("mix", "Miešanie"),
        EFFECT("effect", "Efekt"),
        WAIT("wait", "Čakanie"),
        UNKNOWN("unknown", "Neznámy");
        
        private final String code;
        private final String displayName;
        
        StepType(String code, String displayName) {
            this.code = code;
            this.displayName = displayName;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    @Override
    public String toString() {
        return "RecipeStep{" +
                "type='" + type + '\'' +
                ", instruction='" + instruction + '\'' +
                ", ingredient='" + ingredient + '\'' +
                ", amount=" + amount +
                ", rotations=" + rotations +
                '}';
    }
}
