<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base Application Theme -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/primary_dark_blue</item>
        <item name="colorPrimaryDark">@color/background_dark</item>
        <item name="colorAccent">@color/accent_gold</item>
        <item name="android:windowBackground">@color/background_dark</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:statusBarColor">@color/background_dark</item>
        <item name="android:navigationBarColor">@color/background_dark</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <!-- Splash Screen Theme -->
    <style name="SplashTheme" parent="AppTheme">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <!-- Game Theme -->
    <style name="GameTheme" parent="AppTheme">
        <item name="android:windowBackground">@drawable/game_background</item>
        <item name="android:keepScreenOn">true</item>
    </style>

    <!-- Scanner Theme -->
    <style name="ScannerTheme" parent="AppTheme">
        <item name="android:windowBackground">@color/black</item>
    </style>

    <!-- Button Styles -->
    <style name="MagicButton" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_magic</item>
        <item name="android:textColor">@color/background_dark</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:padding">16dp</item>
        <item name="android:minHeight">60dp</item>
        <item name="android:minWidth">200dp</item>
        <item name="android:elevation">8dp</item>
    </style>

    <style name="SecondaryButton" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_secondary</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:padding">12dp</item>
        <item name="android:minHeight">50dp</item>
        <item name="android:elevation">4dp</item>
    </style>

    <!-- Text Styles -->
    <style name="TitleText">
        <item name="android:textSize">32sp</item>
        <item name="android:textColor">@color/accent_gold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:shadowColor">@color/background_dark</item>
        <item name="android:shadowDx">2</item>
        <item name="android:shadowDy">2</item>
        <item name="android:shadowRadius">4</item>
    </style>

    <style name="SubtitleText">
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="BodyText">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="InstructionText">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/accent_gold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:padding">16dp</item>
        <item name="android:background">@drawable/instruction_background</item>
    </style>

    <!-- Progress Bar Style -->
    <style name="MagicProgressBar" parent="Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:progressDrawable">@drawable/progress_magic</item>
        <item name="android:minHeight">20dp</item>
        <item name="android:maxHeight">20dp</item>
    </style>

    <!-- Card Style -->
    <style name="MagicCard">
        <item name="android:background">@drawable/card_background</item>
        <item name="android:padding">16dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:elevation">8dp</item>
    </style>
</resources>
