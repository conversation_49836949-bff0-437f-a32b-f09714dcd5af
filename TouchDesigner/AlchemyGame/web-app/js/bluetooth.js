/**
 * Čarode<PERSON><PERSON>cka Alchymistická Hra - Bluetooth Manager
 * Spravuje Web Bluetooth API komunikáciu s Raspberry Pi
 */

class BluetoothManager {
    constructor() {
        this.device = null;
        this.server = null;
        this.service = null;
        this.characteristic = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.messageQueue = [];
        this.responseCallbacks = new Map();
        
        // BLE konfigurácia
        this.serviceUUID = '12345678-1234-5678-9012-123456789abc';
        this.characteristicUUID = '*************-8765-2109-cba987654321';
        
        this.init();
    }

    /**
     * Inicializácia Bluetooth managera
     */
    init() {
        console.log('🔵 Inicializujem Bluetooth Manager...');
        
        // Kontrola podpory Web Bluetooth API
        if (!navigator.bluetooth) {
            console.error('❌ Web Bluetooth API nie je podporované');
            this.showBluetoothError('<PERSON><PERSON>š prehliadač nepodporuje Bluetooth');
            return;
        }

        // Nastavenie event listenerov
        this.setupEventListeners();
    }

    /**
     * Nastavenie event listenerov
     */
    setupEventListeners() {
        // Automatické reconnect pri strate spojenia
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible' && !this.isConnected) {
                this.attemptReconnect();
            }
        });
    }

    /**
     * Pripojenie k Raspberry Pi
     */
    async connect() {
        try {
            console.log('🔵 Pripájam sa k Raspberry Pi...');
            
            // Požiadavka o zariadenie
            this.device = await navigator.bluetooth.requestDevice({
                filters: [
                    { services: [this.serviceUUID] },
                    { name: 'AlchemyPi' },
                    { namePrefix: 'Alchemy' }
                ],
                optionalServices: [this.serviceUUID]
            });

            console.log('📱 Zariadenie nájdené:', this.device.name);

            // Event listener pre odpojenie
            this.device.addEventListener('gattserverdisconnected', () => {
                console.warn('⚠️ Zariadenie odpojené');
                this.handleDisconnection();
            });

            // Pripojenie k GATT serveru
            this.server = await this.device.gatt.connect();
            console.log('🔗 GATT server pripojený');

            // Získanie služby
            this.service = await this.server.getPrimaryService(this.serviceUUID);
            console.log('🔧 Služba získaná');

            // Získanie charakteristiky
            this.characteristic = await this.service.getCharacteristic(this.characteristicUUID);
            console.log('📡 Charakteristika získaná');

            // Nastavenie notifikácií
            await this.characteristic.startNotifications();
            this.characteristic.addEventListener('characteristicvaluechanged', (event) => {
                this.handleIncomingMessage(event);
            });

            this.isConnected = true;
            this.reconnectAttempts = 0;
            
            console.log('✅ Bluetooth pripojenie úspešné');
            this.notifyConnectionStatus(true);
            
            // Odoslanie úvodnej správy
            await this.sendMessage({
                action: 'handshake',
                data: { client: 'web-app', version: '1.0' },
                timestamp: Date.now()
            });

            return true;

        } catch (error) {
            console.error('❌ Bluetooth pripojenie zlyhalo:', error);
            this.handleConnectionError(error);
            throw error;
        }
    }

    /**
     * Odpojenie od zariadenia
     */
    async disconnect() {
        try {
            if (this.characteristic) {
                await this.characteristic.stopNotifications();
            }
            
            if (this.server && this.server.connected) {
                this.server.disconnect();
            }
            
            this.cleanup();
            console.log('🔴 Bluetooth odpojené');
            
        } catch (error) {
            console.error('❌ Chyba pri odpojovaní:', error);
        }
    }

    /**
     * Odoslanie správy na Raspberry Pi
     */
    async sendMessage(message, timeout = 5000) {
        if (!this.isConnected || !this.characteristic) {
            throw new Error('Bluetooth nie je pripojené');
        }

        try {
            // Pridanie ID správy pre tracking
            message.id = this.generateMessageId();
            message.timestamp = Date.now();

            const jsonString = JSON.stringify(message);
            const encoder = new TextEncoder();
            const data = encoder.encode(jsonString);

            console.log('📤 Odosielam správu:', message);

            // Odoslanie správy
            await this.characteristic.writeValue(data);

            // Čakanie na odpoveď (ak je potrebné)
            if (message.expectResponse !== false) {
                return new Promise((resolve, reject) => {
                    const timeoutId = setTimeout(() => {
                        this.responseCallbacks.delete(message.id);
                        reject(new Error('Timeout čakania na odpoveď'));
                    }, timeout);

                    this.responseCallbacks.set(message.id, {
                        resolve: (response) => {
                            clearTimeout(timeoutId);
                            resolve(response);
                        },
                        reject: (error) => {
                            clearTimeout(timeoutId);
                            reject(error);
                        }
                    });
                });
            }

        } catch (error) {
            console.error('❌ Chyba pri odosielaní správy:', error);
            throw error;
        }
    }

    /**
     * Spracovanie prichádzajúcich správ
     */
    handleIncomingMessage(event) {
        try {
            const decoder = new TextDecoder();
            const jsonString = decoder.decode(event.target.value);
            const message = JSON.parse(jsonString);

            console.log('📥 Prijatá správa:', message);

            // Spracovanie odpovede na odoslanú správu
            if (message.responseToId && this.responseCallbacks.has(message.responseToId)) {
                const callback = this.responseCallbacks.get(message.responseToId);
                this.responseCallbacks.delete(message.responseToId);
                
                if (message.error) {
                    callback.reject(new Error(message.error));
                } else {
                    callback.resolve(message);
                }
                return;
            }

            // Spracovanie rôznych typov správ
            this.processMessage(message);

        } catch (error) {
            console.error('❌ Chyba pri spracovaní správy:', error);
        }
    }

    /**
     * Spracovanie rôznych typov správ
     */
    processMessage(message) {
        switch (message.status || message.action) {
            case 'weight_result':
                this.handleWeightResult(message.data);
                break;
                
            case 'mixing_progress':
                this.handleMixingProgress(message.data);
                break;
                
            case 'effect_complete':
                this.handleEffectComplete(message.data);
                break;
                
            case 'sensor_reading':
                this.handleSensorReading(message.data);
                break;
                
            case 'error':
                this.handleRemoteError(message.data);
                break;
                
            default:
                console.warn('⚠️ Neznámy typ správy:', message);
        }
    }

    /**
     * Spracovanie výsledku váženia
     */
    handleWeightResult(data) {
        console.log('⚖️ Výsledok váženia:', data);
        
        // Aktualizácia UI
        const weightReading = document.getElementById('weight-reading');
        if (weightReading) {
            weightReading.textContent = `${data.current_weight}g`;
        }

        // Notifikácia hernej logiky
        if (window.alchemyGame && window.alchemyGame.gameLogic) {
            window.alchemyGame.gameLogic.handleWeightResult(data);
        }
    }

    /**
     * Spracovanie pokroku miešania
     */
    handleMixingProgress(data) {
        console.log('🥄 Pokrok miešania:', data);
        
        // Aktualizácia UI
        const mixingStatus = document.getElementById('mixing-status');
        if (mixingStatus) {
            mixingStatus.textContent = `${data.current_rotations}/${data.target_rotations}`;
        }

        // Notifikácia hernej logiky
        if (window.alchemyGame && window.alchemyGame.gameLogic) {
            window.alchemyGame.gameLogic.handleMixingProgress(data);
        }
    }

    /**
     * Spracovanie dokončenia efektu
     */
    handleEffectComplete(data) {
        console.log('✨ Efekt dokončený:', data);
        
        // Notifikácia hernej logiky
        if (window.alchemyGame && window.alchemyGame.gameLogic) {
            window.alchemyGame.gameLogic.handleEffectComplete(data);
        }
    }

    /**
     * Spracovanie čítania senzorov
     */
    handleSensorReading(data) {
        // Aktualizácia UI s údajmi zo senzorov
        if (data.weight !== undefined) {
            const weightReading = document.getElementById('weight-reading');
            if (weightReading) {
                weightReading.textContent = `${data.weight}g`;
            }
        }

        if (data.nfc_detected) {
            console.log('📡 NFC detekované:', data.nfc_id);
        }
    }

    /**
     * Spracovanie chyby zo vzdialeného zariadenia
     */
    handleRemoteError(data) {
        console.error('❌ Chyba z Raspberry Pi:', data);
        
        if (window.alchemyGame) {
            window.alchemyGame.showError(`Chyba zariadenia: ${data.message}`);
        }
    }

    /**
     * Spracovanie straty spojenia
     */
    handleDisconnection() {
        this.isConnected = false;
        this.notifyConnectionStatus(false);
        
        // Pokus o automatické reconnect
        setTimeout(() => {
            this.attemptReconnect();
        }, 2000);
    }

    /**
     * Pokus o opätovné pripojenie
     */
    async attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('❌ Maximálny počet pokusov o reconnect dosiahnutý');
            return;
        }

        this.reconnectAttempts++;
        console.log(`🔄 Pokus o reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);

        try {
            if (this.device && this.device.gatt.connected) {
                // Zariadenie je stále pripojené, len obnovíme služby
                await this.reconnectServices();
            } else {
                // Úplné opätovné pripojenie
                await this.connect();
            }
        } catch (error) {
            console.error('❌ Reconnect zlyhal:', error);
            
            // Čakanie pred ďalším pokusom
            setTimeout(() => {
                this.attemptReconnect();
            }, 5000);
        }
    }

    /**
     * Obnovenie služieb
     */
    async reconnectServices() {
        try {
            this.service = await this.server.getPrimaryService(this.serviceUUID);
            this.characteristic = await this.service.getCharacteristic(this.characteristicUUID);
            
            await this.characteristic.startNotifications();
            this.characteristic.addEventListener('characteristicvaluechanged', (event) => {
                this.handleIncomingMessage(event);
            });

            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.notifyConnectionStatus(true);
            
            console.log('✅ Služby obnovené');
            
        } catch (error) {
            throw new Error('Nepodarilo sa obnoviť služby: ' + error.message);
        }
    }

    /**
     * Notifikácia o stave pripojenia
     */
    notifyConnectionStatus(connected) {
        // Aktualizácia UI
        if (window.alchemyGame) {
            window.alchemyGame.updateBluetoothStatus(connected);
        }

        // Custom event pre ostatné komponenty
        const event = new CustomEvent('bluetoothStatusChanged', {
            detail: { connected }
        });
        document.dispatchEvent(event);
    }

    /**
     * Spracovanie chyby pripojenia
     */
    handleConnectionError(error) {
        let errorMessage = 'Nepodarilo sa pripojiť k zariadeniu';
        
        if (error.name === 'NotFoundError') {
            errorMessage = 'Zariadenie nebolo nájdené. Skontrolujte, či je Raspberry Pi zapnuté.';
        } else if (error.name === 'SecurityError') {
            errorMessage = 'Bezpečnostná chyba. Skúste obnoviť stránku.';
        } else if (error.name === 'NetworkError') {
            errorMessage = 'Chyba siete. Skontrolujte Bluetooth pripojenie.';
        }

        this.showBluetoothError(errorMessage);
    }

    /**
     * Zobrazenie Bluetooth chyby
     */
    showBluetoothError(message) {
        if (window.alchemyGame) {
            window.alchemyGame.showError(message);
        } else {
            alert(message);
        }
    }

    /**
     * Generovanie ID správy
     */
    generateMessageId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Vyčistenie zdrojov
     */
    cleanup() {
        this.device = null;
        this.server = null;
        this.service = null;
        this.characteristic = null;
        this.isConnected = false;
        this.responseCallbacks.clear();
        this.messageQueue = [];
    }

    /**
     * Kontrola stavu pripojenia
     */
    isDeviceConnected() {
        return this.isConnected && 
               this.device && 
               this.device.gatt && 
               this.device.gatt.connected;
    }

    /**
     * Získanie informácií o zariadení
     */
    getDeviceInfo() {
        if (!this.device) return null;
        
        return {
            name: this.device.name,
            id: this.device.id,
            connected: this.isConnected
        };
    }
}

// Export pre ostatné moduly
window.BluetoothManager = BluetoothManager;
