/**
 * Čarodejnícka Alchymistická Hra - QR Scanner
 * Spravuje skenovanie QR kódov pre výber receptov
 */

class QRScanner {
    constructor(gameApp) {
        this.gameApp = gameApp;
        this.video = null;
        this.canvas = null;
        this.context = null;
        this.stream = null;
        this.isScanning = false;
        this.scanInterval = null;
        this.lastScanTime = 0;
        this.scanCooldown = 2000; // 2 sekundy medzi skenovaním
        
        this.init();
    }

    /**
     * Inicializácia QR scannera
     */
    init() {
        console.log('📷 Inicializujem QR Scanner...');
        
        this.video = document.getElementById('qr-video');
        this.setupCanvas();
        this.setupEventListeners();
        
        // Kontrola podpory kamery
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            console.error('❌ Kamera nie je podporovaná');
            this.showCameraError('<PERSON><PERSON><PERSON> prehlia<PERSON> nepodporuje kameru');
            return;
        }
    }

    /**
     * Nastavenie canvas pre spracovanie obrazu
     */
    setupCanvas() {
        this.canvas = document.createElement('canvas');
        this.context = this.canvas.getContext('2d');
        
        // Canvas nebude viditeľný, slúži len na spracovanie
        this.canvas.style.display = 'none';
        document.body.appendChild(this.canvas);
    }

    /**
     * Nastavenie event listenerov
     */
    setupEventListeners() {
        // Klik na QR video pre manuálne skenovanie
        if (this.video) {
            this.video.addEventListener('click', () => {
                this.manualScan();
            });
        }

        // Zmena veľkosti okna
        window.addEventListener('resize', () => {
            this.adjustVideoSize();
        });
    }

    /**
     * Spustenie skenovania
     */
    async startScanning() {
        if (this.isScanning) {
            console.warn('⚠️ Skenovanie už beží');
            return;
        }

        try {
            console.log('📷 Spúšťam kameru...');
            
            // Požiadavka o prístup ku kamere
            this.stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'environment', // Zadná kamera ak je dostupná
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                }
            });

            // Nastavenie video streamu
            this.video.srcObject = this.stream;
            this.video.play();

            // Čakanie na načítanie videa
            await new Promise((resolve) => {
                this.video.addEventListener('loadedmetadata', resolve, { once: true });
            });

            this.adjustVideoSize();
            this.isScanning = true;
            
            // Spustenie skenovacieho cyklu
            this.startScanLoop();
            
            console.log('✅ QR Scanner spustený');

        } catch (error) {
            console.error('❌ Chyba pri spustení kamery:', error);
            this.handleCameraError(error);
        }
    }

    /**
     * Zastavenie skenovania
     */
    stopScanning() {
        if (!this.isScanning) {
            return;
        }

        console.log('📷 Zastavujem QR Scanner...');

        // Zastavenie skenovacieho cyklu
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
            this.scanInterval = null;
        }

        // Zastavenie video streamu
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }

        // Vyčistenie video elementu
        if (this.video) {
            this.video.srcObject = null;
        }

        this.isScanning = false;
        console.log('✅ QR Scanner zastavený');
    }

    /**
     * Spustenie skenovacieho cyklu
     */
    startScanLoop() {
        this.scanInterval = setInterval(() => {
            this.scanFrame();
        }, 500); // Skenovanie každých 500ms
    }

    /**
     * Skenovanie jedného snímku
     */
    scanFrame() {
        if (!this.video || !this.canvas || !this.context) {
            return;
        }

        try {
            // Nastavenie veľkosti canvas podľa videa
            this.canvas.width = this.video.videoWidth;
            this.canvas.height = this.video.videoHeight;

            // Nakreslenie aktuálneho snímku na canvas
            this.context.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);

            // Získanie obrazových dát
            const imageData = this.context.getImageData(0, 0, this.canvas.width, this.canvas.height);

            // Skenovanie QR kódu
            const qrCode = this.detectQRCode(imageData);
            
            if (qrCode) {
                this.handleQRCodeDetected(qrCode);
            }

        } catch (error) {
            console.error('❌ Chyba pri skenovaní snímku:', error);
        }
    }

    /**
     * Detekcia QR kódu v obraze
     * Poznámka: Toto je zjednodušená implementácia
     * V reálnej aplikácii by sa použila knižnica ako jsQR
     */
    detectQRCode(imageData) {
        // Simulácia detekcie QR kódu
        // V reálnej implementácii by tu bola knižnica jsQR alebo podobná
        
        // Pre testovanie - simulujeme nájdenie QR kódu po 3 sekundách
        if (!this.simulatedQRFound && Date.now() - this.lastScanTime > 3000) {
            this.simulatedQRFound = true;
            return 'RECIPE_001'; // Simulovaný QR kód
        }
        
        return null;
    }

    /**
     * Spracovanie nájdeného QR kódu
     */
    handleQRCodeDetected(qrCode) {
        const now = Date.now();
        
        // Kontrola cooldown
        if (now - this.lastScanTime < this.scanCooldown) {
            return;
        }

        this.lastScanTime = now;
        
        console.log('📱 QR kód nájdený:', qrCode);
        
        // Vizuálny feedback
        this.showQRFoundAnimation();
        
        // Vyhľadanie receptu
        const recipe = this.findRecipeByQRCode(qrCode);
        
        if (recipe) {
            console.log('📜 Recept nájdený:', recipe.name);
            
            // Zastavenie skenovania
            this.stopScanning();
            
            // Nastavenie receptu v hre
            this.gameApp.setCurrentRecipe(recipe);
            
            // Zvukový efekt
            if (this.gameApp.audioManager) {
                this.gameApp.audioManager.playSound('qr_found');
            }
            
        } else {
            console.warn('⚠️ Recept pre QR kód nebol nájdený:', qrCode);
            this.showQRError('Neznámy QR kód. Skúste iný recept.');
        }
    }

    /**
     * Vyhľadanie receptu podľa QR kódu
     */
    findRecipeByQRCode(qrCode) {
        if (!this.gameApp.recipes || !this.gameApp.recipes.recipes) {
            return null;
        }

        return this.gameApp.recipes.recipes.find(recipe => recipe.qr_code === qrCode);
    }

    /**
     * Manuálne skenovanie (pre testovanie)
     */
    manualScan() {
        console.log('👆 Manuálne skenovanie...');
        
        // Pre testovanie - simulujeme nájdenie prvého receptu
        if (this.gameApp.recipes && this.gameApp.recipes.recipes.length > 0) {
            const firstRecipe = this.gameApp.recipes.recipes[0];
            this.handleQRCodeDetected(firstRecipe.qr_code);
        }
    }

    /**
     * Zobrazenie animácie nájdeného QR kódu
     */
    showQRFoundAnimation() {
        const qrFrame = document.querySelector('.qr-frame');
        const qrInstruction = document.querySelector('.qr-instruction');
        
        if (qrFrame) {
            qrFrame.classList.add('qr-found');
            setTimeout(() => {
                qrFrame.classList.remove('qr-found');
            }, 2000);
        }
        
        if (qrInstruction) {
            const originalText = qrInstruction.textContent;
            qrInstruction.textContent = '✅ QR kód nájdený!';
            qrInstruction.style.color = '#00FF00';
            
            setTimeout(() => {
                qrInstruction.textContent = originalText;
                qrInstruction.style.color = '';
            }, 2000);
        }
    }

    /**
     * Zobrazenie chyby QR kódu
     */
    showQRError(message) {
        const qrInstruction = document.querySelector('.qr-instruction');
        
        if (qrInstruction) {
            const originalText = qrInstruction.textContent;
            qrInstruction.textContent = message;
            qrInstruction.style.color = '#FF0000';
            
            setTimeout(() => {
                qrInstruction.textContent = originalText;
                qrInstruction.style.color = '';
            }, 3000);
        }
    }

    /**
     * Prispôsobenie veľkosti videa
     */
    adjustVideoSize() {
        if (!this.video) return;

        const container = this.video.parentElement;
        if (!container) return;

        const containerRect = container.getBoundingClientRect();
        const videoAspectRatio = this.video.videoWidth / this.video.videoHeight;
        const containerAspectRatio = containerRect.width / containerRect.height;

        if (videoAspectRatio > containerAspectRatio) {
            // Video je širšie ako kontajner
            this.video.style.width = '100%';
            this.video.style.height = 'auto';
        } else {
            // Video je vyššie ako kontajner
            this.video.style.width = 'auto';
            this.video.style.height = '100%';
        }
    }

    /**
     * Spracovanie chyby kamery
     */
    handleCameraError(error) {
        let errorMessage = 'Nepodarilo sa spustiť kameru';
        
        if (error.name === 'NotAllowedError') {
            errorMessage = 'Prístup ku kamere bol zamietnutý. Povoľte prístup ku kamere.';
        } else if (error.name === 'NotFoundError') {
            errorMessage = 'Kamera nebola nájdená. Skontrolujte pripojenie kamery.';
        } else if (error.name === 'NotSupportedError') {
            errorMessage = 'Kamera nie je podporovaná v tomto prehliadači.';
        } else if (error.name === 'NotReadableError') {
            errorMessage = 'Kamera je používaná inou aplikáciou.';
        }

        this.showCameraError(errorMessage);
    }

    /**
     * Zobrazenie chyby kamery
     */
    showCameraError(message) {
        console.error('❌ Camera error:', message);
        
        // Zobrazenie chyby v QR kontajneri
        const qrContainer = document.querySelector('.qr-scanner-container');
        if (qrContainer) {
            qrContainer.innerHTML = `
                <div class="camera-error">
                    <div class="error-icon">📷</div>
                    <h3>Chyba kamery</h3>
                    <p>${message}</p>
                    <button class="magic-button" onclick="location.reload()">Obnoviť stránku</button>
                    <button class="magic-button secondary" onclick="window.alchemyGame.qrScanner.manualScan()">Testovací recept</button>
                </div>
            `;
        }
        
        // Zobrazenie chyby v hlavnej aplikácii
        if (this.gameApp) {
            this.gameApp.showError(message);
        }
    }

    /**
     * Získanie stavu scannera
     */
    getStatus() {
        return {
            isScanning: this.isScanning,
            hasCamera: !!this.stream,
            lastScanTime: this.lastScanTime
        };
    }

    /**
     * Reštart scannera
     */
    async restart() {
        console.log('🔄 Reštartujem QR Scanner...');
        
        this.stopScanning();
        
        // Krátka pauza pred reštartom
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        await this.startScanning();
    }

    /**
     * Vyčistenie zdrojov
     */
    cleanup() {
        this.stopScanning();
        
        if (this.canvas && this.canvas.parentNode) {
            this.canvas.parentNode.removeChild(this.canvas);
        }
        
        this.video = null;
        this.canvas = null;
        this.context = null;
        this.stream = null;
    }
}

// Export pre ostatné moduly
window.QRScanner = QRScanner;
