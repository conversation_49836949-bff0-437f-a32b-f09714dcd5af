/**
 * Čarodejnícka Alchymistická Hra - Audio Manager
 * Spravuje všetky zvuky a hudbu v hre
 */

class AudioManager {
    constructor() {
        this.sounds = new Map();
        this.backgroundMusic = null;
        this.isMuted = false;
        this.masterVolume = 0.7;
        this.musicVolume = 0.5;
        this.effectsVolume = 0.8;
        this.isInitialized = false;
        
        this.init();
    }

    /**
     * Inicializácia audio managera
     */
    init() {
        console.log('🔊 Inicializujem Audio Manager...');
        
        // Kontrola podpory Web Audio API
        if (!window.AudioContext && !window.webkitAudioContext) {
            console.warn('⚠️ Web Audio API nie je podporované');
        }
        
        this.loadSounds();
        this.setupEventListeners();
    }

    /**
     * Načítanie všetkých zvukov
     */
    async loadSounds() {
        const soundFiles = {
            // Background hudba
            'background_music': 'assets/sounds/background_music.mp3',
            
            // <PERSON>vukov<PERSON> efekty
            'step_complete': 'assets/sounds/step_complete.mp3',
            'success_sound': 'assets/sounds/success_sound.mp3',
            'failure_sound': 'assets/sounds/failure_sound.mp3',
            'qr_found': 'assets/sounds/qr_found.mp3',
            'button_click': 'assets/sounds/button_click.mp3',
            'mixing_sound': 'assets/sounds/mixing_sound.mp3',
            'pouring_sound': 'assets/sounds/pouring_sound.mp3',
            'magic_sparkle': 'assets/sounds/magic_sparkle.mp3',
            'timer_warning': 'assets/sounds/timer_warning.mp3',
            'timer_critical': 'assets/sounds/timer_critical.mp3'
        };

        console.log('📂 Načítavam zvukové súbory...');

        for (const [name, path] of Object.entries(soundFiles)) {
            try {
                const audio = await this.loadSound(path);
                this.sounds.set(name, audio);
                console.log(`✅ Načítaný zvuk: ${name}`);
            } catch (error) {
                console.warn(`⚠️ Nepodarilo sa načítať zvuk ${name}:`, error);
                // Vytvorenie prázdneho audio objektu ako fallback
                this.sounds.set(name, this.createSilentAudio());
            }
        }

        // Nastavenie background hudby
        this.backgroundMusic = this.sounds.get('background_music');
        if (this.backgroundMusic) {
            this.backgroundMusic.loop = true;
            this.backgroundMusic.volume = this.musicVolume * this.masterVolume;
        }

        this.isInitialized = true;
        console.log('✅ Audio Manager inicializovaný');
    }

    /**
     * Načítanie jedného zvukového súboru
     */
    loadSound(path) {
        return new Promise((resolve, reject) => {
            const audio = new Audio();
            
            audio.addEventListener('canplaythrough', () => {
                resolve(audio);
            }, { once: true });
            
            audio.addEventListener('error', (error) => {
                reject(error);
            }, { once: true });
            
            audio.src = path;
            audio.preload = 'auto';
        });
    }

    /**
     * Vytvorenie tichého audio objektu (fallback)
     */
    createSilentAudio() {
        const audio = new Audio();
        audio.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
        return audio;
    }

    /**
     * Nastavenie event listenerov
     */
    setupEventListeners() {
        // Automatické spustenie audio kontextu po prvom kliknutí
        document.addEventListener('click', () => {
            this.resumeAudioContext();
        }, { once: true });

        // Nastavenie hlasitosti pre všetky tlačidlá
        document.addEventListener('click', (event) => {
            if (event.target.classList.contains('magic-button')) {
                this.playSound('button_click');
            }
        });

        // Klávesové skratky pre audio
        document.addEventListener('keydown', (event) => {
            if (event.key === 'm' || event.key === 'M') {
                this.toggleMute();
            }
        });
    }

    /**
     * Obnovenie audio kontextu (potrebné pre autoplay policy)
     */
    resumeAudioContext() {
        if (window.AudioContext || window.webkitAudioContext) {
            const AudioContext = window.AudioContext || window.webkitAudioContext;
            if (this.audioContext && this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }
        }
    }

    /**
     * Prehranie zvuku
     */
    playSound(soundName, volume = 1.0) {
        if (!this.isInitialized || this.isMuted) {
            return;
        }

        const sound = this.sounds.get(soundName);
        if (!sound) {
            console.warn(`⚠️ Zvuk '${soundName}' nebol nájdený`);
            return;
        }

        try {
            // Klonovanie audio objektu pre súčasné prehrávanie
            const audioClone = sound.cloneNode();
            audioClone.volume = volume * this.effectsVolume * this.masterVolume;
            
            // Prehranie zvuku
            const playPromise = audioClone.play();
            
            if (playPromise !== undefined) {
                playPromise.catch(error => {
                    console.warn(`⚠️ Nepodarilo sa prehrať zvuk '${soundName}':`, error);
                });
            }

            console.log(`🔊 Prehrávam zvuk: ${soundName}`);

        } catch (error) {
            console.error(`❌ Chyba pri prehrávaní zvuku '${soundName}':`, error);
        }
    }

    /**
     * Spustenie background hudby
     */
    playBackgroundMusic() {
        if (!this.backgroundMusic || this.isMuted) {
            return;
        }

        try {
            this.backgroundMusic.currentTime = 0;
            this.backgroundMusic.volume = this.musicVolume * this.masterVolume;
            
            const playPromise = this.backgroundMusic.play();
            
            if (playPromise !== undefined) {
                playPromise
                    .then(() => {
                        console.log('🎵 Background hudba spustená');
                    })
                    .catch(error => {
                        console.warn('⚠️ Nepodarilo sa spustiť background hudbu:', error);
                    });
            }

        } catch (error) {
            console.error('❌ Chyba pri spustení background hudby:', error);
        }
    }

    /**
     * Zastavenie background hudby
     */
    stopBackgroundMusic() {
        if (!this.backgroundMusic) {
            return;
        }

        try {
            this.backgroundMusic.pause();
            this.backgroundMusic.currentTime = 0;
            console.log('🎵 Background hudba zastavená');
        } catch (error) {
            console.error('❌ Chyba pri zastavení background hudby:', error);
        }
    }

    /**
     * Pozastavenie background hudby
     */
    pauseBackgroundMusic() {
        if (!this.backgroundMusic) {
            return;
        }

        try {
            this.backgroundMusic.pause();
            console.log('⏸️ Background hudba pozastavená');
        } catch (error) {
            console.error('❌ Chyba pri pozastavení background hudby:', error);
        }
    }

    /**
     * Pokračovanie background hudby
     */
    resumeBackgroundMusic() {
        if (!this.backgroundMusic || this.isMuted) {
            return;
        }

        try {
            const playPromise = this.backgroundMusic.play();
            
            if (playPromise !== undefined) {
                playPromise
                    .then(() => {
                        console.log('▶️ Background hudba pokračuje');
                    })
                    .catch(error => {
                        console.warn('⚠️ Nepodarilo sa pokračovať v background hudbe:', error);
                    });
            }

        } catch (error) {
            console.error('❌ Chyba pri pokračovaní background hudby:', error);
        }
    }

    /**
     * Prepnutie stlmenia
     */
    toggleMute() {
        this.isMuted = !this.isMuted;
        
        if (this.isMuted) {
            this.pauseBackgroundMusic();
            console.log('🔇 Audio stlmené');
        } else {
            this.resumeBackgroundMusic();
            console.log('🔊 Audio zapnuté');
        }

        // Aktualizácia UI
        this.updateMuteButton();
    }

    /**
     * Nastavenie master hlasitosti
     */
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        
        // Aktualizácia hlasitosti background hudby
        if (this.backgroundMusic) {
            this.backgroundMusic.volume = this.musicVolume * this.masterVolume;
        }

        console.log(`🔊 Master hlasitosť nastavená na: ${Math.round(this.masterVolume * 100)}%`);
    }

    /**
     * Nastavenie hlasitosti hudby
     */
    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        
        if (this.backgroundMusic) {
            this.backgroundMusic.volume = this.musicVolume * this.masterVolume;
        }

        console.log(`🎵 Hlasitosť hudby nastavená na: ${Math.round(this.musicVolume * 100)}%`);
    }

    /**
     * Nastavenie hlasitosti efektov
     */
    setEffectsVolume(volume) {
        this.effectsVolume = Math.max(0, Math.min(1, volume));
        console.log(`🔊 Hlasitosť efektov nastavená na: ${Math.round(this.effectsVolume * 100)}%`);
    }

    /**
     * Aktualizácia tlačidla stlmenia
     */
    updateMuteButton() {
        const muteButton = document.getElementById('mute-button');
        if (muteButton) {
            muteButton.textContent = this.isMuted ? '🔇' : '🔊';
            muteButton.title = this.isMuted ? 'Zapnúť zvuk' : 'Stlmiť zvuk';
        }
    }

    /**
     * Prehranie sekvencie zvukov
     */
    async playSoundSequence(sounds, delay = 500) {
        for (const soundName of sounds) {
            this.playSound(soundName);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    /**
     * Prehranie zvuku s fade efektom
     */
    playSoundWithFade(soundName, fadeInDuration = 1000, fadeOutDuration = 1000) {
        const sound = this.sounds.get(soundName);
        if (!sound || this.isMuted) {
            return;
        }

        const audioClone = sound.cloneNode();
        audioClone.volume = 0;

        // Fade in
        const fadeInInterval = setInterval(() => {
            if (audioClone.volume < this.effectsVolume * this.masterVolume) {
                audioClone.volume += 0.05;
            } else {
                clearInterval(fadeInInterval);
            }
        }, fadeInDuration / 20);

        // Prehranie
        audioClone.play().catch(error => {
            console.warn(`⚠️ Nepodarilo sa prehrať zvuk s fade '${soundName}':`, error);
        });

        // Fade out pred koncom
        audioClone.addEventListener('timeupdate', () => {
            const remaining = audioClone.duration - audioClone.currentTime;
            if (remaining <= fadeOutDuration / 1000 && remaining > 0) {
                const fadeOutInterval = setInterval(() => {
                    if (audioClone.volume > 0.05) {
                        audioClone.volume -= 0.05;
                    } else {
                        audioClone.volume = 0;
                        clearInterval(fadeOutInterval);
                    }
                }, fadeOutDuration / 20);
            }
        });
    }

    /**
     * Získanie stavu audio managera
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            isMuted: this.isMuted,
            masterVolume: this.masterVolume,
            musicVolume: this.musicVolume,
            effectsVolume: this.effectsVolume,
            soundsLoaded: this.sounds.size,
            backgroundMusicPlaying: this.backgroundMusic && !this.backgroundMusic.paused
        };
    }

    /**
     * Vyčistenie zdrojov
     */
    cleanup() {
        console.log('🧹 Čistím Audio Manager...');
        
        this.stopBackgroundMusic();
        
        // Zastavenie všetkých zvukov
        this.sounds.forEach(sound => {
            if (sound && !sound.paused) {
                sound.pause();
                sound.currentTime = 0;
            }
        });

        this.sounds.clear();
        this.backgroundMusic = null;
        this.isInitialized = false;
    }
}

// Export pre ostatné moduly
window.AudioManager = AudioManager;
