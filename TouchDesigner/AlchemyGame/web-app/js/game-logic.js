/**
 * Čarodejnícka Alchymistická Hra - Herná logika
 * Spravuje herný proces, kroky receptu a hodnotenie
 */

class GameLogic {
    constructor(gameApp) {
        this.gameApp = gameApp;
        this.currentStepData = null;
        this.stepStartTime = null;
        this.stepTimeouts = new Map();
        this.scoreMultiplier = 1.0;
        
        this.init();
    }

    /**
     * Inicializácia hernej logiky
     */
    init() {
        console.log('🎮 Inicializujem hernú logiku...');
        this.setupStepHandlers();
    }

    /**
     * Nastavenie handlerov pre rôzne typy krokov
     */
    setupStepHandlers() {
        this.stepHandlers = {
            'weigh': this.handleWeighStep.bind(this),
            'mix': this.handleMixStep.bind(this),
            'effect': this.handleEffectStep.bind(this)
        };
    }

    /**
     * Spustenie kroku receptu
     */
    startStep(stepData) {
        this.currentStepData = stepData;
        this.stepStartTime = Date.now();
        
        console.log(`🔄 Spúšťam krok ${stepData.step}:`, stepData.instruction);
        
        // Vymazanie predchádzajúcich timeoutov
        this.clearStepTimeouts();
        
        // Spustenie handlera pre daný typ kroku
        const handler = this.stepHandlers[stepData.type];
        if (handler) {
            handler(stepData);
        } else {
            console.error('❌ Neznámy typ kroku:', stepData.type);
        }
    }

    /**
     * Spracovanie kroku váženia
     */
    async handleWeighStep(stepData) {
        console.log('⚖️ Krok váženia:', stepData);
        
        // Aktualizácia UI
        this.updateStepUI(stepData);
        this.showIngredientDisplay(stepData);
        
        // Požiadavka na kontrolu váhy
        try {
            if (this.gameApp.bluetoothManager && this.gameApp.bluetoothManager.isConnected) {
                await this.gameApp.bluetoothManager.sendMessage({
                    action: 'check_weight',
                    data: {
                        ingredient: stepData.ingredient,
                        target_weight: stepData.amount,
                        tolerance: stepData.tolerance
                    }
                });
            } else {
                // Simulácia pre testovanie bez Bluetooth
                setTimeout(() => {
                    this.simulateWeightResult(stepData);
                }, 2000);
            }
        } catch (error) {
            console.error('❌ Chyba pri kontrole váhy:', error);
            this.gameApp.showError('Chyba pri komunikácii s váhou');
        }
    }

    /**
     * Spracovanie kroku miešania
     */
    async handleMixStep(stepData) {
        console.log('🥄 Krok miešania:', stepData);
        
        // Aktualizácia UI
        this.updateStepUI(stepData);
        this.showMixingDisplay(stepData);
        
        // Požiadavka na spustenie miešania
        try {
            if (this.gameApp.bluetoothManager && this.gameApp.bluetoothManager.isConnected) {
                await this.gameApp.bluetoothManager.sendMessage({
                    action: 'start_mixing',
                    data: {
                        direction: stepData.direction,
                        rotations: stepData.rotations,
                        speed: stepData.speed
                    }
                });
            } else {
                // Simulácia pre testovanie bez Bluetooth
                this.simulateMixingProgress(stepData);
            }
        } catch (error) {
            console.error('❌ Chyba pri spustení miešania:', error);
            this.gameApp.showError('Chyba pri komunikácii s miešačom');
        }
    }

    /**
     * Spracovanie kroku efektu
     */
    async handleEffectStep(stepData) {
        console.log('✨ Krok efektu:', stepData);
        
        // Aktualizácia UI
        this.updateStepUI(stepData);
        this.showEffectDisplay(stepData);
        
        // Požiadavka na aktiváciu efektu
        try {
            if (this.gameApp.bluetoothManager && this.gameApp.bluetoothManager.isConnected) {
                await this.gameApp.bluetoothManager.sendMessage({
                    action: 'activate_effect',
                    data: {
                        type: stepData.effect_type,
                        duration: stepData.duration,
                        led_color: this.getEffectColor(stepData.effect_type)
                    }
                });
            } else {
                // Simulácia pre testovanie bez Bluetooth
                setTimeout(() => {
                    this.handleEffectComplete({
                        type: stepData.effect_type,
                        duration: stepData.duration,
                        success: true
                    });
                }, stepData.duration * 1000);
            }
        } catch (error) {
            console.error('❌ Chyba pri aktivácii efektu:', error);
            this.gameApp.showError('Chyba pri komunikácii s efektmi');
        }
    }

    /**
     * Spracovanie výsledku váženia
     */
    handleWeightResult(data) {
        console.log('⚖️ Výsledok váženia:', data);
        
        if (!this.currentStepData || this.currentStepData.type !== 'weigh') {
            return;
        }

        const isCorrect = data.is_correct;
        const stepTime = Date.now() - this.stepStartTime;
        
        // Aktualizácia UI
        this.updateWeightDisplay(data);
        
        if (isCorrect) {
            // Úspešný krok
            this.completeStep(true, stepTime);
            this.showStepSuccess();
            
            // Zvukový efekt
            if (this.gameApp.audioManager) {
                this.gameApp.audioManager.playSound('step_complete');
            }
        } else {
            // Neúspešný krok - daj hráčovi ešte jednu šancu
            this.gameApp.gameState.errors++;
            this.showStepError('Nesprávna hmotnosť! Skús znova.');
            
            // Zníženie skóre
            this.adjustScore(-10);
        }
    }

    /**
     * Spracovanie pokroku miešania
     */
    handleMixingProgress(data) {
        console.log('🥄 Pokrok miešania:', data);
        
        if (!this.currentStepData || this.currentStepData.type !== 'mix') {
            return;
        }

        // Aktualizácia UI
        this.updateMixingDisplay(data);
        
        if (data.is_complete) {
            const stepTime = Date.now() - this.stepStartTime;
            this.completeStep(true, stepTime);
            this.showStepSuccess();
            
            // Zvukový efekt
            if (this.gameApp.audioManager) {
                this.gameApp.audioManager.playSound('step_complete');
            }
        }
    }

    /**
     * Spracovanie dokončenia efektu
     */
    handleEffectComplete(data) {
        console.log('✨ Efekt dokončený:', data);
        
        if (!this.currentStepData || this.currentStepData.type !== 'effect') {
            return;
        }

        const stepTime = Date.now() - this.stepStartTime;
        
        if (data.success) {
            this.completeStep(true, stepTime);
            this.showStepSuccess();
            
            // Zvukový efekt
            if (this.gameApp.audioManager) {
                this.gameApp.audioManager.playSound('step_complete');
            }
        } else {
            this.gameApp.gameState.errors++;
            this.showStepError('Efekt sa nepodaril!');
            this.adjustScore(-20);
        }
    }

    /**
     * Dokončenie kroku
     */
    completeStep(success, stepTime) {
        if (!success) {
            return;
        }

        // Výpočet bodov za krok
        const basePoints = 50;
        const timeBonus = Math.max(0, 30 - Math.floor(stepTime / 1000));
        const stepPoints = (basePoints + timeBonus) * this.scoreMultiplier;
        
        this.adjustScore(stepPoints);
        
        // Zobrazenie tlačidla pre ďalší krok
        const nextStepButton = document.getElementById('next-step');
        if (nextStepButton) {
            nextStepButton.style.display = 'block';
        }
        
        console.log(`✅ Krok dokončený! Získané body: ${stepPoints}`);
    }

    /**
     * Úprava skóre
     */
    adjustScore(points) {
        this.gameApp.gameState.score += points;
        
        // Aktualizácia UI
        const scoreElement = document.getElementById('current-score');
        if (scoreElement) {
            scoreElement.textContent = this.gameApp.gameState.score;
            
            // Animácia zmeny skóre
            if (points > 0) {
                scoreElement.classList.add('animate-score-increase');
            } else {
                scoreElement.classList.add('animate-score-decrease');
            }
            
            setTimeout(() => {
                scoreElement.classList.remove('animate-score-increase', 'animate-score-decrease');
            }, 1000);
        }
    }

    /**
     * Aktualizácia UI kroku
     */
    updateStepUI(stepData) {
        const stepTitle = document.getElementById('step-title');
        const stepDescription = document.getElementById('step-description');
        const stepHint = document.getElementById('step-hint');
        
        if (stepTitle) stepTitle.textContent = stepData.instruction;
        if (stepDescription) stepDescription.textContent = stepData.hint || '';
        if (stepHint) {
            stepHint.textContent = stepData.hint || '';
            stepHint.style.display = stepData.hint ? 'block' : 'none';
        }
        
        // Skrytie tlačidla ďalšieho kroku
        const nextStepButton = document.getElementById('next-step');
        if (nextStepButton) {
            nextStepButton.style.display = 'none';
        }
    }

    /**
     * Zobrazenie ingrediencie
     */
    showIngredientDisplay(stepData) {
        const ingredientDisplay = document.getElementById('ingredient-display');
        const mixingDisplay = document.getElementById('mixing-display');
        
        if (ingredientDisplay) {
            ingredientDisplay.style.display = 'block';
            ingredientDisplay.innerHTML = this.createIngredientHTML(stepData);
        }
        
        if (mixingDisplay) {
            mixingDisplay.style.display = 'none';
        }
    }

    /**
     * Zobrazenie miešania
     */
    showMixingDisplay(stepData) {
        const ingredientDisplay = document.getElementById('ingredient-display');
        const mixingDisplay = document.getElementById('mixing-display');
        
        if (ingredientDisplay) {
            ingredientDisplay.style.display = 'none';
        }
        
        if (mixingDisplay) {
            mixingDisplay.style.display = 'block';
            this.updateMixingInstructions(stepData);
        }
    }

    /**
     * Zobrazenie efektu
     */
    showEffectDisplay(stepData) {
        const ingredientDisplay = document.getElementById('ingredient-display');
        const mixingDisplay = document.getElementById('mixing-display');
        
        if (ingredientDisplay) {
            ingredientDisplay.style.display = 'none';
        }
        
        if (mixingDisplay) {
            mixingDisplay.style.display = 'block';
            mixingDisplay.innerHTML = `
                <div class="effect-container">
                    <div class="effect-animation ${stepData.effect_type}">
                        <p>Sleduj magický efekt...</p>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Vytvorenie HTML pre ingredienciu
     */
    createIngredientHTML(stepData) {
        const ingredient = this.getIngredientInfo(stepData.ingredient);
        
        return `
            <div class="ingredient-item" data-ingredient="${stepData.ingredient}">
                <div class="ingredient-image">
                    <img src="assets/images/ingredients/${ingredient.image}" alt="${ingredient.name}">
                </div>
                <div class="ingredient-info">
                    <h4>${ingredient.name}</h4>
                    <p class="ingredient-amount">Potrebné: ${stepData.amount}g (±${stepData.tolerance}g)</p>
                    <p class="ingredient-description">${ingredient.description}</p>
                </div>
                <div class="weight-indicator">
                    <div class="weight-display">
                        <span id="current-weight">0.0g</span>
                    </div>
                    <div class="weight-status" id="weight-status">
                        <span>Pridaj ingredienciu na váhu</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Aktualizácia inštrukcií miešania
     */
    updateMixingInstructions(stepData) {
        const mixingDirection = document.getElementById('mixing-direction');
        const currentRotations = document.getElementById('current-rotations');
        const targetRotations = document.getElementById('target-rotations');
        
        if (mixingDirection) {
            const directionText = stepData.direction === 'clockwise' ? 'doprava' : 'doľava';
            const speedText = this.getSpeedText(stepData.speed);
            mixingDirection.textContent = `Miešaj ${directionText} - ${speedText}`;
        }
        
        if (currentRotations) currentRotations.textContent = '0';
        if (targetRotations) targetRotations.textContent = stepData.rotations;
    }

    /**
     * Aktualizácia zobrazenia váhy
     */
    updateWeightDisplay(data) {
        const currentWeight = document.getElementById('current-weight');
        const weightStatus = document.getElementById('weight-status');
        
        if (currentWeight) {
            currentWeight.textContent = `${data.current_weight}g`;
        }
        
        if (weightStatus) {
            if (data.is_correct) {
                weightStatus.innerHTML = '<span class="success">✅ Správna hmotnosť!</span>';
                weightStatus.classList.add('success');
            } else {
                const diff = Math.abs(data.current_weight - data.target_weight);
                weightStatus.innerHTML = `<span class="error">❌ Odchýlka: ${diff.toFixed(1)}g</span>`;
                weightStatus.classList.add('error');
            }
        }
    }

    /**
     * Aktualizácia zobrazenia miešania
     */
    updateMixingDisplay(data) {
        const currentRotations = document.getElementById('current-rotations');
        const cauldronContent = document.querySelector('.cauldron-content');
        
        if (currentRotations) {
            currentRotations.textContent = data.current_rotations;
        }
        
        if (cauldronContent) {
            // Animácia miešania
            const rotationClass = data.direction === 'clockwise' ? 'mixing-clockwise' : 'mixing-counterclockwise';
            cauldronContent.classList.add(rotationClass);
            
            if (data.is_complete) {
                setTimeout(() => {
                    cauldronContent.classList.remove(rotationClass);
                }, 1000);
            }
        }
    }

    /**
     * Zobrazenie úspechu kroku
     */
    showStepSuccess() {
        // Animácia úspechu
        const stepContainer = document.querySelector('.current-step-container');
        if (stepContainer) {
            stepContainer.classList.add('animate-success');
            setTimeout(() => {
                stepContainer.classList.remove('animate-success');
            }, 2000);
        }
    }

    /**
     * Zobrazenie chyby kroku
     */
    showStepError(message) {
        // Animácia chyby
        const stepContainer = document.querySelector('.current-step-container');
        if (stepContainer) {
            stepContainer.classList.add('animate-error');
            setTimeout(() => {
                stepContainer.classList.remove('animate-error');
            }, 2000);
        }
        
        // Zobrazenie chybovej správy
        this.gameApp.showError(message);
    }

    /**
     * Získanie informácií o ingrediencii
     */
    getIngredientInfo(ingredientId) {
        if (this.gameApp.recipes && this.gameApp.recipes.ingredients) {
            return this.gameApp.recipes.ingredients[ingredientId] || {
                name: ingredientId,
                description: 'Neznáma ingrediencia',
                image: 'default.png'
            };
        }
        
        return {
            name: ingredientId,
            description: 'Neznáma ingrediencia',
            image: 'default.png'
        };
    }

    /**
     * Získanie farby efektu
     */
    getEffectColor(effectType) {
        const colors = {
            'fog': '#8A2BE2',
            'sparkle': '#FFD700',
            'glow': '#00FF00',
            'fire': '#FF4500'
        };
        
        return colors[effectType] || '#FFD700';
    }

    /**
     * Získanie textu rýchlosti
     */
    getSpeedText(speed) {
        const speeds = {
            'slow': 'pomaly',
            'medium': 'stredne',
            'fast': 'rýchlo'
        };
        
        return speeds[speed] || speed;
    }

    /**
     * Simulácia výsledku váženia (pre testovanie)
     */
    simulateWeightResult(stepData) {
        const randomWeight = stepData.amount + (Math.random() - 0.5) * stepData.tolerance * 2;
        const isCorrect = Math.abs(randomWeight - stepData.amount) <= stepData.tolerance;
        
        this.handleWeightResult({
            current_weight: parseFloat(randomWeight.toFixed(1)),
            target_weight: stepData.amount,
            tolerance: stepData.tolerance,
            is_correct: isCorrect
        });
    }

    /**
     * Simulácia pokroku miešania (pre testovanie)
     */
    simulateMixingProgress(stepData) {
        let currentRotations = 0;
        
        const interval = setInterval(() => {
            currentRotations++;
            
            this.handleMixingProgress({
                current_rotations: currentRotations,
                target_rotations: stepData.rotations,
                direction: stepData.direction,
                is_complete: currentRotations >= stepData.rotations
            });
            
            if (currentRotations >= stepData.rotations) {
                clearInterval(interval);
            }
        }, 1000);
    }

    /**
     * Vymazanie timeoutov kroku
     */
    clearStepTimeouts() {
        this.stepTimeouts.forEach(timeout => clearTimeout(timeout));
        this.stepTimeouts.clear();
    }
}

// Export pre ostatné moduly
window.GameLogic = GameLogic;
