/**
 * Čarodejnícka Alchymistická Hra - Hlavný JavaScript súbor
 * Spravuje celkovú logiku aplikácie, obrazovky a používateľské rozhranie
 */

class AlchemyGameApp {
    constructor() {
        this.currentScreen = 'splash-screen';
        this.gameState = {
            isGameActive: false,
            currentRecipe: null,
            currentStep: 0,
            score: 0,
            timeRemaining: 900, // 15 minút v sekundách
            errors: 0,
            startTime: null
        };
        
        this.screens = {
            splash: document.getElementById('splash-screen'),
            intro: document.getElementById('intro-screen'),
            recipe: document.getElementById('recipe-screen'),
            game: document.getElementById('game-screen'),
            result: document.getElementById('result-screen')
        };
        
        this.init();
    }

    /**
     * Inicializácia aplikácie
     */
    init() {
        console.log('🧙‍♂️ Inicializujem Čarodejnícku Alchymistickú Hru...');
        
        // Nastavenie fullscreen a kiosk mode
        this.setupKioskMode();
        
        // Načítanie receptov
        this.loadRecipes();
        
        // Nastavenie event listenerov
        this.setupEventListeners();
        
        // Spustenie splash screen
        this.showSplashScreen();
        
        // Inicializácia modulov
        this.initializeModules();
    }

    /**
     * Nastavenie kiosk mode pre tablet
     */
    setupKioskMode() {
        // Zákaz kontextového menu
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            return false;
        });

        // Zákaz klávesových skratiek
        document.addEventListener('keydown', (e) => {
            // Zákaz F11, ESC, Alt+Tab, Ctrl+W, atď.
            if (e.key === 'F11' || 
                e.key === 'Escape' || 
                (e.altKey && e.key === 'Tab') ||
                (e.ctrlKey && e.key === 'w') ||
                (e.ctrlKey && e.key === 'W') ||
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                e.key === 'F12') {
                e.preventDefault();
                return false;
            }
        });

        // Automatické fullscreen
        document.addEventListener('click', () => {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.warn('Fullscreen request failed:', err);
                });
            }
        }, { once: true });

        // Zákaz výberu textu
        document.onselectstart = () => false;
        document.ondragstart = () => false;
    }

    /**
     * Načítanie receptov z JSON súboru
     */
    async loadRecipes() {
        try {
            const response = await fetch('data/recipes.json');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            this.recipes = await response.json();
            console.log('✅ Recepty načítané:', this.recipes.recipes.length);
        } catch (error) {
            console.error('❌ Chyba pri načítavaní receptov:', error);
            // Fallback recepty
            this.recipes = this.getFallbackRecipes();
        }
    }

    /**
     * Záložné recepty ak sa nepodarí načítať zo súboru
     */
    getFallbackRecipes() {
        return {
            recipes: [
                {
                    id: 1,
                    name: "Elixír Múdrosti",
                    difficulty: "easy",
                    time_limit: 900,
                    qr_code: "RECIPE_001",
                    description: "Základný elixír pre začiatočníkov",
                    steps: [
                        {
                            step: 1,
                            type: "weigh",
                            ingredient: "dragons_blood",
                            amount: 3.0,
                            tolerance: 0.2,
                            instruction: "Odmeraj 3g Dračej krvi do zlatej misky",
                            hint: "Buď opatrný, dračia krv je veľmi silná!"
                        },
                        {
                            step: 2,
                            type: "mix",
                            direction: "clockwise",
                            rotations: 5,
                            speed: "slow",
                            instruction: "Miešaj 5x doprava pomalým tempom",
                            hint: "Pomaly a sústreďte sa na mágiu!"
                        }
                    ],
                    success_message: "Gratulujeme! Vytvoril si Elixír Múdrosti!",
                    failure_message: "Ups! Vznikla žabacie šťava. Skús znova!"
                }
            ]
        };
    }

    /**
     * Nastavenie event listenerov
     */
    setupEventListeners() {
        // Intro video
        const introVideo = document.getElementById('intro-video');
        const skipIntro = document.getElementById('skip-intro');
        
        if (introVideo) {
            introVideo.addEventListener('ended', () => this.showRecipeScreen());
        }
        
        if (skipIntro) {
            skipIntro.addEventListener('click', () => this.showRecipeScreen());
        }

        // Recipe screen
        const startRecipe = document.getElementById('start-recipe');
        if (startRecipe) {
            startRecipe.addEventListener('click', () => this.startGame());
        }

        // Game screen
        const bluetoothConnect = document.getElementById('bluetooth-connect');
        const nextStep = document.getElementById('next-step');
        const restartGame = document.getElementById('restart-game');

        if (bluetoothConnect) {
            bluetoothConnect.addEventListener('click', () => this.connectBluetooth());
        }

        if (nextStep) {
            nextStep.addEventListener('click', () => this.nextStep());
        }

        if (restartGame) {
            restartGame.addEventListener('click', () => this.restartGame());
        }

        // Result screen
        const playAgain = document.getElementById('play-again');
        const newRecipe = document.getElementById('new-recipe');

        if (playAgain) {
            playAgain.addEventListener('click', () => this.playAgain());
        }

        if (newRecipe) {
            newRecipe.addEventListener('click', () => this.showRecipeScreen());
        }

        // Error modal
        const errorOk = document.getElementById('error-ok');
        if (errorOk) {
            errorOk.addEventListener('click', () => this.hideErrorModal());
        }
    }

    /**
     * Inicializácia modulov
     */
    initializeModules() {
        // Inicializácia bude dokončená po načítaní ostatných modulov
        document.addEventListener('DOMContentLoaded', () => {
            if (window.BluetoothManager) {
                this.bluetoothManager = new BluetoothManager();
            }
            
            if (window.GameLogic) {
                this.gameLogic = new GameLogic(this);
            }
            
            if (window.QRScanner) {
                this.qrScanner = new QRScanner(this);
            }
            
            if (window.AudioManager) {
                this.audioManager = new AudioManager();
            }
        });
    }

    /**
     * Zobrazenie splash screen
     */
    showSplashScreen() {
        this.switchScreen('splash-screen');
        
        // Simulácia načítavania
        setTimeout(() => {
            this.showIntroScreen();
        }, 3000);
    }

    /**
     * Zobrazenie intro video
     */
    showIntroScreen() {
        this.switchScreen('intro-screen');
        
        const introVideo = document.getElementById('intro-video');
        if (introVideo) {
            introVideo.currentTime = 0;
            introVideo.play().catch(err => {
                console.warn('Video autoplay failed:', err);
                // Ak sa nepodarí prehrať video, prejdi na ďalšiu obrazovku
                setTimeout(() => this.showRecipeScreen(), 2000);
            });
        }
    }

    /**
     * Zobrazenie výberu receptu
     */
    showRecipeScreen() {
        this.switchScreen('recipe-screen');
        
        // Spustenie QR scannera
        if (this.qrScanner) {
            this.qrScanner.startScanning();
        }
    }

    /**
     * Spustenie hry
     */
    startGame() {
        if (!this.gameState.currentRecipe) {
            this.showError('Najprv vyberte recept!');
            return;
        }

        this.switchScreen('game-screen');
        this.gameState.isGameActive = true;
        this.gameState.startTime = Date.now();
        this.gameState.currentStep = 0;
        this.gameState.score = 0;
        this.gameState.errors = 0;
        this.gameState.timeRemaining = this.gameState.currentRecipe.time_limit || 900;

        // Spustenie timera
        this.startTimer();
        
        // Zobrazenie prvého kroku
        this.showCurrentStep();
        
        // Spustenie background hudby
        if (this.audioManager) {
            this.audioManager.playBackgroundMusic();
        }

        console.log('🎮 Hra spustená:', this.gameState.currentRecipe.name);
    }

    /**
     * Prepnutie na ďalší krok
     */
    nextStep() {
        if (!this.gameState.isGameActive) return;

        this.gameState.currentStep++;
        
        if (this.gameState.currentStep >= this.gameState.currentRecipe.steps.length) {
            this.endGame(true);
        } else {
            this.showCurrentStep();
        }
    }

    /**
     * Zobrazenie aktuálneho kroku
     */
    showCurrentStep() {
        const recipe = this.gameState.currentRecipe;
        const step = recipe.steps[this.gameState.currentStep];
        
        if (!step) return;

        // Aktualizácia UI
        document.getElementById('current-recipe-name').textContent = recipe.name;
        document.getElementById('current-step').textContent = this.gameState.currentStep + 1;
        document.getElementById('total-steps').textContent = recipe.steps.length;
        document.getElementById('step-title').textContent = step.instruction;
        document.getElementById('step-description').textContent = step.hint || '';
        
        // Zobrazenie step hint
        const stepHint = document.getElementById('step-hint');
        if (stepHint && step.hint) {
            stepHint.textContent = step.hint;
            stepHint.style.display = 'block';
        }

        console.log(`📋 Krok ${this.gameState.currentStep + 1}:`, step.instruction);
    }

    /**
     * Spustenie timera
     */
    startTimer() {
        this.timerInterval = setInterval(() => {
            this.gameState.timeRemaining--;
            this.updateTimerDisplay();
            
            if (this.gameState.timeRemaining <= 0) {
                this.endGame(false);
            }
        }, 1000);
    }

    /**
     * Aktualizácia zobrazenia timera
     */
    updateTimerDisplay() {
        const minutes = Math.floor(this.gameState.timeRemaining / 60);
        const seconds = this.gameState.timeRemaining % 60;
        const timeText = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        const timerElement = document.getElementById('timer-text');
        if (timerElement) {
            timerElement.textContent = timeText;
            
            // Zmena farby pri malom čase
            const timerCircle = timerElement.closest('.timer-circle');
            if (this.gameState.timeRemaining <= 60) {
                timerCircle.classList.add('timer-critical');
            } else if (this.gameState.timeRemaining <= 300) {
                timerCircle.classList.add('timer-warning');
            }
        }
    }

    /**
     * Pripojenie Bluetooth zariadenia
     */
    async connectBluetooth() {
        if (!this.bluetoothManager) {
            this.showError('Bluetooth manager nie je dostupný');
            return;
        }

        try {
            await this.bluetoothManager.connect();
            this.updateBluetoothStatus(true);
        } catch (error) {
            console.error('Bluetooth connection failed:', error);
            this.showError('Nepodarilo sa pripojiť k zariadeniu');
        }
    }

    /**
     * Aktualizácia Bluetooth statusu
     */
    updateBluetoothStatus(connected) {
        const indicator = document.getElementById('bluetooth-indicator');
        const statusText = document.getElementById('bluetooth-status-text');
        
        if (indicator && statusText) {
            if (connected) {
                indicator.classList.add('connected');
                statusText.textContent = 'Pripojené';
            } else {
                indicator.classList.remove('connected');
                statusText.textContent = 'Odpojené';
            }
        }
    }

    /**
     * Ukončenie hry
     */
    endGame(success) {
        this.gameState.isGameActive = false;
        
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }

        // Výpočet finálneho skóre
        const finalScore = this.calculateFinalScore(success);
        
        // Zobrazenie výsledku
        this.showResultScreen(success, finalScore);
        
        // Zastavenie background hudby
        if (this.audioManager) {
            this.audioManager.stopBackgroundMusic();
        }

        console.log('🏁 Hra ukončená:', success ? 'Úspech' : 'Neúspech', 'Skóre:', finalScore);
    }

    /**
     * Výpočet finálneho skóre
     */
    calculateFinalScore(success) {
        if (!success) return 0;

        const timeUsed = 900 - this.gameState.timeRemaining;
        const timeBonus = Math.max(0, 200 - Math.floor(timeUsed / 3));
        const accuracyBonus = (this.gameState.currentStep + 1) * 50;
        const perfectBonus = this.gameState.errors === 0 ? 100 : 0;
        
        return timeBonus + accuracyBonus + perfectBonus;
    }

    /**
     * Zobrazenie výsledkovej obrazovky
     */
    showResultScreen(success, score) {
        this.switchScreen('result-screen');
        
        const resultTitle = document.getElementById('result-title');
        const finalScore = document.getElementById('final-score');
        const resultMessage = document.getElementById('result-message');
        const resultVideo = document.getElementById('result-video');
        const resultVideoSource = document.getElementById('result-video-source');
        
        if (success) {
            resultTitle.textContent = 'Gratulujeme!';
            resultMessage.textContent = this.gameState.currentRecipe.success_message;
            resultVideoSource.src = 'assets/videos/success_ending.mp4';
        } else {
            resultTitle.textContent = 'Skús to znova!';
            resultMessage.textContent = this.gameState.currentRecipe.failure_message;
            resultVideoSource.src = 'assets/videos/failure_ending.mp4';
        }
        
        finalScore.textContent = score;
        
        // Prehranie výsledkového videa
        resultVideo.load();
        resultVideo.play().catch(err => console.warn('Result video autoplay failed:', err));
    }

    /**
     * Reštart hry
     */
    restartGame() {
        this.gameState = {
            isGameActive: false,
            currentRecipe: this.gameState.currentRecipe,
            currentStep: 0,
            score: 0,
            timeRemaining: 900,
            errors: 0,
            startTime: null
        };
        
        this.startGame();
    }

    /**
     * Hrať znova s rovnakým receptom
     */
    playAgain() {
        this.restartGame();
    }

    /**
     * Prepnutie obrazovky
     */
    switchScreen(screenId) {
        // Skryť všetky obrazovky
        Object.values(this.screens).forEach(screen => {
            if (screen) {
                screen.classList.remove('active');
            }
        });
        
        // Zobraziť požadovanú obrazovku
        const targetScreen = document.getElementById(screenId);
        if (targetScreen) {
            targetScreen.classList.add('active');
            this.currentScreen = screenId;
        }
    }

    /**
     * Zobrazenie chybovej správy
     */
    showError(message) {
        const errorModal = document.getElementById('error-modal');
        const errorMessage = document.getElementById('error-message');
        
        if (errorModal && errorMessage) {
            errorMessage.textContent = message;
            errorModal.classList.add('active');
        }
        
        console.error('❌ Error:', message);
    }

    /**
     * Skrytie chybovej správy
     */
    hideErrorModal() {
        const errorModal = document.getElementById('error-modal');
        if (errorModal) {
            errorModal.classList.remove('active');
        }
    }

    /**
     * Nastavenie aktuálneho receptu
     */
    setCurrentRecipe(recipe) {
        this.gameState.currentRecipe = recipe;
        
        // Aktualizácia UI
        const recipeName = document.getElementById('recipe-name');
        const recipeDescription = document.getElementById('recipe-description');
        const recipeInfo = document.getElementById('recipe-info');
        
        if (recipeName) recipeName.textContent = recipe.name;
        if (recipeDescription) recipeDescription.textContent = recipe.description;
        if (recipeInfo) recipeInfo.style.display = 'block';
        
        console.log('📜 Recept vybraný:', recipe.name);
    }
}

// Spustenie aplikácie po načítaní stránky
document.addEventListener('DOMContentLoaded', () => {
    window.alchemyGame = new AlchemyGameApp();
});

// Export pre ostatné moduly
window.AlchemyGameApp = AlchemyGameApp;
