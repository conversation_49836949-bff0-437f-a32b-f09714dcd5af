/* <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Resp<PERSON><PERSON><PERSON><PERSON><PERSON> */

/* <PERSON><PERSON><PERSON><PERSON><PERSON> nastaven<PERSON> pre tablet 1920x1200 */
@media screen and (min-width: 1200px) and (max-width: 1920px) {
    .game-title {
        font-size: 4.5rem;
    }
    
    .screen-title {
        font-size: 3rem;
    }
    
    .magic-button {
        padding: 20px 40px;
        font-size: 1.4rem;
        min-width: 180px;
    }
    
    .qr-scanner-container {
        width: 500px;
        height: 500px;
    }
    
    .qr-frame {
        width: 250px;
        height: 250px;
    }
    
    .timer-circle {
        width: 100px;
        height: 100px;
    }
    
    #timer-text {
        font-size: 1.4rem;
    }
    
    #current-score {
        font-size: 1.8rem;
    }
    
    .current-step-container {
        gap: 4rem;
    }
    
    .step-instruction {
        padding: 2.5rem;
    }
    
    #step-title {
        font-size: 2.2rem;
    }
    
    #step-description {
        font-size: 1.4rem;
    }
}

/* <PERSON><PERSON><PERSON> tablety a laptopy */
@media screen and (min-width: 768px) and (max-width: 1199px) {
    .game-title {
        font-size: 3.5rem;
    }
    
    .screen-title {
        font-size: 2.2rem;
    }
    
    .magic-button {
        padding: 15px 30px;
        font-size: 1.1rem;
        min-width: 140px;
    }
    
    .qr-scanner-container {
        width: 350px;
        height: 350px;
    }
    
    .qr-frame {
        width: 180px;
        height: 180px;
    }
    
    .timer-circle {
        width: 70px;
        height: 70px;
    }
    
    #timer-text {
        font-size: 1rem;
    }
    
    #current-score {
        font-size: 1.3rem;
    }
    
    .current-step-container {
        flex-direction: column;
        gap: 2rem;
    }
    
    .step-instruction {
        padding: 1.5rem;
    }
    
    #step-title {
        font-size: 1.6rem;
    }
    
    #step-description {
        font-size: 1.1rem;
    }
    
    .game-content {
        margin-top: 100px;
    }
}

/* Mobilné zariadenia (pre testovanie) */
@media screen and (max-width: 767px) {
    .game-title {
        font-size: 2.5rem;
    }
    
    .screen-title {
        font-size: 1.8rem;
    }
    
    .magic-button {
        padding: 12px 24px;
        font-size: 1rem;
        min-width: 120px;
    }
    
    .qr-scanner-container {
        width: 280px;
        height: 280px;
    }
    
    .qr-frame {
        width: 140px;
        height: 140px;
    }
    
    .timer-circle {
        width: 60px;
        height: 60px;
    }
    
    #timer-text {
        font-size: 0.9rem;
    }
    
    #current-score {
        font-size: 1.2rem;
    }
    
    .current-step-container {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .step-instruction {
        padding: 1rem;
    }
    
    #step-title {
        font-size: 1.4rem;
    }
    
    #step-description {
        font-size: 1rem;
    }
    
    .game-header {
        flex-direction: column;
        gap: 10px;
        align-items: center;
    }
    
    .game-content {
        margin-top: 140px;
        width: 95%;
    }
    
    .status-panel {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .sensor-readings {
        justify-content: center;
        gap: 1rem;
    }
}

/* Landscape orientácia (preferovaná pre tablet) */
@media screen and (orientation: landscape) {
    body {
        overflow: hidden;
    }
    
    .game-header {
        flex-direction: row;
    }
    
    .current-step-container {
        flex-direction: row;
    }
    
    .status-panel {
        flex-direction: row;
    }
}

/* Portrait orientácia (núdzové riešenie) */
@media screen and (orientation: portrait) {
    .game-title {
        font-size: 3rem;
    }
    
    .current-step-container {
        flex-direction: column;
    }
    
    .game-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .game-content {
        margin-top: 160px;
    }
    
    .status-panel {
        flex-direction: column;
        gap: 10px;
    }
    
    /* Upozornenie na otočenie */
    body::before {
        content: "Pre najlepší zážitok otočte zariadenie na šírku";
        position: fixed;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(255, 0, 0, 0.8);
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-size: 0.9rem;
        z-index: 9999;
        animation: pulse 2s infinite;
    }
}

/* Vysoké rozlíšenie (4K a vyššie) */
@media screen and (min-width: 2560px) {
    .game-title {
        font-size: 6rem;
    }
    
    .screen-title {
        font-size: 4rem;
    }
    
    .magic-button {
        padding: 25px 50px;
        font-size: 1.8rem;
        min-width: 220px;
    }
    
    .qr-scanner-container {
        width: 600px;
        height: 600px;
    }
    
    .qr-frame {
        width: 300px;
        height: 300px;
    }
    
    .timer-circle {
        width: 120px;
        height: 120px;
    }
    
    #timer-text {
        font-size: 1.6rem;
    }
    
    #current-score {
        font-size: 2.2rem;
    }
    
    .step-instruction {
        padding: 3rem;
    }
    
    #step-title {
        font-size: 2.8rem;
    }
    
    #step-description {
        font-size: 1.6rem;
    }
}

/* Nízke rozlíšenie (starší hardware) */
@media screen and (max-width: 1024px) and (max-height: 768px) {
    .game-title {
        font-size: 2.8rem;
    }
    
    .loading-spinner {
        width: 40px;
        height: 40px;
    }
    
    .magic-button {
        padding: 10px 20px;
        font-size: 0.9rem;
        min-width: 100px;
    }
    
    .modal-content {
        padding: 1.5rem;
        max-width: 400px;
    }
    
    .modal-content h3 {
        font-size: 1.4rem;
    }
    
    .modal-content p {
        font-size: 1rem;
    }
}

/* Prispôsobenie pre dotykovú obrazovku */
@media (pointer: coarse) {
    .magic-button {
        min-height: 60px;
        min-width: 160px;
        padding: 18px 35px;
    }
    
    .qr-scanner-container {
        border-width: 4px;
    }
    
    .timer-circle {
        border-width: 5px;
    }
    
    /* Väčšie touch targets */
    .skip-button {
        min-height: 50px;
        min-width: 120px;
        padding: 15px 25px;
    }
    
    .status-indicator {
        width: 16px;
        height: 16px;
    }
}

/* Prispôsobenie pre myš a klávesnicu */
@media (pointer: fine) {
    .magic-button:hover {
        cursor: pointer;
    }
    
    .skip-button:hover {
        cursor: pointer;
    }
    
    /* Jemnejšie hover efekty */
    .magic-button:hover {
        transform: translateY(-2px) scale(1.02);
    }
}

/* Redukované animácie pre slabší hardware */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .loading-spinner {
        animation: none;
        border: 4px solid #FFD700;
    }
    
    .magical-glow {
        animation: none;
        text-shadow: 0 0 20px #FFD700;
    }
}

/* Tmavý režim (ak je podporovaný) */
@media (prefers-color-scheme: dark) {
    /* Už máme tmavý dizajn, takže len jemné úpravy */
    body {
        background: linear-gradient(135deg, #0F0F23 0%, #1F1F3F 50%, #4A1A4A 100%);
    }
    
    .screen {
        background: inherit;
    }
}

/* Svetlý režim (núdzové riešenie) */
@media (prefers-color-scheme: light) {
    /* Zachováme tmavý magický dizajn aj v svetlom režime */
    body {
        background: linear-gradient(135deg, #191970 0%, #2F4F4F 50%, #8A2BE2 100%);
    }
}

/* Print štýly (pre dokumentáciu) */
@media print {
    body {
        background: white !important;
        color: black !important;
    }
    
    .screen {
        position: static !important;
        display: block !important;
    }
    
    .magic-button {
        background: white !important;
        color: black !important;
        border: 2px solid black !important;
    }
    
    video, audio {
        display: none !important;
    }
    
    .loading-spinner {
        display: none !important;
    }
}
