/* Čaro<PERSON><PERSON><PERSON><PERSON><PERSON> Alchymistická Hra - CSS Animácie */

/* <PERSON><PERSON>ladn<PERSON> animá<PERSON> */
@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

@keyframes slideInLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Magické efekty */
@keyframes magical-glow {
    0% {
        text-shadow: 0 0 20px #FFD700, 0 0 40px #FFD700;
        transform: scale(1);
    }
    50% {
        text-shadow: 0 0 30px #FFD700, 0 0 60px #FFD700, 0 0 80px #8A2BE2;
        transform: scale(1.02);
    }
    100% {
        text-shadow: 0 0 20px #FFD700, 0 0 40px #FFD700;
        transform: scale(1);
    }
}

@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* QR Scanner animácie */
@keyframes qr-scan {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7);
    }
    50% {
        box-shadow: 0 0 0 20px rgba(255, 215, 0, 0.3);
    }
    100% {
        box-shadow: 0 0 0 40px rgba(255, 215, 0, 0);
    }
}

@keyframes qr-found {
    0% {
        transform: scale(1);
        border-color: #FFD700;
    }
    50% {
        transform: scale(1.1);
        border-color: #00FF00;
        box-shadow: 0 0 30px #00FF00;
    }
    100% {
        transform: scale(1);
        border-color: #00FF00;
    }
}

/* Ingrediencie animácie */
@keyframes ingredient-highlight {
    0% {
        transform: scale(1);
        box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 25px rgba(255, 215, 0, 0.8);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
    }
}

@keyframes ingredient-pour {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: translateY(-20px) rotate(15deg);
        opacity: 0.8;
    }
    100% {
        transform: translateY(50px) rotate(30deg);
        opacity: 0;
    }
}

@keyframes ingredient-correct {
    0% {
        background-color: rgba(255, 215, 0, 0.1);
        border-color: #FFD700;
    }
    50% {
        background-color: rgba(0, 255, 0, 0.3);
        border-color: #00FF00;
        transform: scale(1.05);
    }
    100% {
        background-color: rgba(0, 255, 0, 0.1);
        border-color: #00FF00;
        transform: scale(1);
    }
}

@keyframes ingredient-wrong {
    0% {
        background-color: rgba(255, 215, 0, 0.1);
        border-color: #FFD700;
    }
    25% {
        background-color: rgba(255, 0, 0, 0.3);
        border-color: #FF0000;
        transform: translateX(-5px);
    }
    50% {
        transform: translateX(5px);
    }
    75% {
        transform: translateX(-5px);
    }
    100% {
        background-color: rgba(255, 0, 0, 0.1);
        border-color: #FF0000;
        transform: translateX(0);
    }
}

/* Miešanie animácie */
@keyframes cauldron-bubble {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0.8;
    }
}

@keyframes mixing-clockwise {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes mixing-counterclockwise {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(-360deg);
    }
}

@keyframes liquid-swirl {
    0% {
        background: radial-gradient(circle, #8A2BE2 0%, #FFD700 50%, #191970 100%);
        transform: rotate(0deg);
    }
    33% {
        background: radial-gradient(circle, #FFD700 0%, #191970 50%, #8A2BE2 100%);
        transform: rotate(120deg);
    }
    66% {
        background: radial-gradient(circle, #191970 0%, #8A2BE2 50%, #FFD700 100%);
        transform: rotate(240deg);
    }
    100% {
        background: radial-gradient(circle, #8A2BE2 0%, #FFD700 50%, #191970 100%);
        transform: rotate(360deg);
    }
}

/* Efekty úspešnosti */
@keyframes success-sparkle {
    0% {
        opacity: 0;
        transform: scale(0) rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: scale(1.2) rotate(180deg);
    }
    100% {
        opacity: 0;
        transform: scale(0) rotate(360deg);
    }
}

@keyframes success-glow {
    0% {
        box-shadow: 0 0 20px #00FF00;
    }
    50% {
        box-shadow: 0 0 40px #00FF00, 0 0 60px #FFD700;
    }
    100% {
        box-shadow: 0 0 20px #00FF00;
    }
}

@keyframes failure-shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* Timer animácie */
@keyframes timer-warning {
    0% {
        border-color: #FFD700;
        color: #FFD700;
    }
    50% {
        border-color: #FF8C00;
        color: #FF8C00;
    }
    100% {
        border-color: #FFD700;
        color: #FFD700;
    }
}

@keyframes timer-critical {
    0% {
        border-color: #FF0000;
        color: #FF0000;
        transform: scale(1);
    }
    50% {
        border-color: #FF4500;
        color: #FF4500;
        transform: scale(1.1);
    }
    100% {
        border-color: #FF0000;
        color: #FF0000;
        transform: scale(1);
    }
}

/* Skóre animácie */
@keyframes score-increase {
    0% {
        transform: scale(1);
        color: #FFD700;
    }
    50% {
        transform: scale(1.3);
        color: #00FF00;
        text-shadow: 0 0 20px #00FF00;
    }
    100% {
        transform: scale(1);
        color: #FFD700;
        text-shadow: none;
    }
}

@keyframes score-decrease {
    0% {
        transform: scale(1);
        color: #FFD700;
    }
    50% {
        transform: scale(1.2);
        color: #FF0000;
        text-shadow: 0 0 20px #FF0000;
    }
    100% {
        transform: scale(1);
        color: #FFD700;
        text-shadow: none;
    }
}

/* Bluetooth status animácie */
@keyframes bluetooth-connecting {
    0% {
        background: #FFD700;
        transform: scale(1);
    }
    50% {
        background: #FF8C00;
        transform: scale(1.2);
    }
    100% {
        background: #FFD700;
        transform: scale(1);
    }
}

@keyframes bluetooth-connected {
    0% {
        background: #00FF00;
        box-shadow: 0 0 5px #00FF00;
    }
    50% {
        background: #32CD32;
        box-shadow: 0 0 15px #00FF00;
    }
    100% {
        background: #00FF00;
        box-shadow: 0 0 5px #00FF00;
    }
}

@keyframes bluetooth-error {
    0% {
        background: #FF0000;
        transform: scale(1);
    }
    25% {
        transform: scale(1.1);
    }
    50% {
        background: #FF4500;
        transform: scale(1.2);
    }
    75% {
        transform: scale(1.1);
    }
    100% {
        background: #FF0000;
        transform: scale(1);
    }
}

/* Utility classes pre animácie */
.animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-fadeOut {
    animation: fadeOut 0.5s ease-in-out;
}

.animate-slideInLeft {
    animation: slideInLeft 0.5s ease-out;
}

.animate-slideInRight {
    animation: slideInRight 0.5s ease-out;
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-glow {
    animation: magical-glow 3s infinite;
}

/* Hover efekty */
.magic-button:hover {
    animation: magical-glow 0.5s ease-in-out;
}

.ingredient-item:hover {
    animation: ingredient-highlight 1s ease-in-out;
}

/* Responsive animácie */
@media (max-width: 768px) {
    @keyframes magical-glow {
        0% {
            text-shadow: 0 0 10px #FFD700, 0 0 20px #FFD700;
        }
        50% {
            text-shadow: 0 0 15px #FFD700, 0 0 30px #FFD700, 0 0 40px #8A2BE2;
        }
        100% {
            text-shadow: 0 0 10px #FFD700, 0 0 20px #FFD700;
        }
    }
}
