/* <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Alchymist<PERSON> - <PERSON><PERSON><PERSON><PERSON> */

/* Reset a základné nastavenia */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cin<PERSON>', serif;
    background: linear-gradient(135deg, #191970 0%, #2F4F4F 50%, #8A2BE2 100%);
    color: #FFD700;
    overflow: hidden;
    height: 100vh;
    width: 100vw;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Zákaz kontextového menu */
body {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Obrazovky */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    align-items: center;
    justify-content: center;
    background: inherit;
}

.screen.active {
    display: flex;
}

/* Splash Screen */
#splash-screen {
    flex-direction: column;
    background: radial-gradient(circle, #2F4F4F 0%, #191970 100%);
}

.logo-container {
    text-align: center;
    animation: fadeInUp 2s ease-out;
}

.game-title {
    font-family: 'MedievalSharp', cursive;
    font-size: 4rem;
    color: #FFD700;
    text-shadow: 0 0 20px #FFD700, 0 0 40px #FFD700;
    margin-bottom: 2rem;
    animation: magical-glow 3s infinite;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 215, 0, 0.3);
    border-top: 4px solid #FFD700;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 2rem auto;
}

.loading-text {
    font-size: 1.5rem;
    color: #DDD;
    animation: pulse 2s infinite;
}

/* Video prehrávač */
.fullscreen-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.skip-button {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: #FFD700;
    border: 2px solid #FFD700;
    padding: 10px 20px;
    border-radius: 25px;
    font-family: 'Cinzel', serif;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.skip-button:hover {
    background: #FFD700;
    color: #191970;
    transform: scale(1.05);
}

/* Recipe Selection Screen */
.recipe-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 90%;
    max-width: 800px;
}

.screen-title {
    font-family: 'MedievalSharp', cursive;
    font-size: 2.5rem;
    color: #FFD700;
    text-align: center;
    margin-bottom: 2rem;
    text-shadow: 0 0 10px #FFD700;
}

.qr-scanner-container {
    position: relative;
    width: 400px;
    height: 400px;
    border: 3px solid #FFD700;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
}

.qr-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.qr-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
}

.qr-frame {
    width: 200px;
    height: 200px;
    border: 3px solid #FFD700;
    border-radius: 10px;
    position: relative;
    animation: qr-scan 2s infinite;
}

.qr-frame::before,
.qr-frame::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 30px;
    border: 3px solid #FFD700;
}

.qr-frame::before {
    top: -3px;
    left: -3px;
    border-right: none;
    border-bottom: none;
}

.qr-frame::after {
    bottom: -3px;
    right: -3px;
    border-left: none;
    border-top: none;
}

.qr-instruction {
    color: #FFD700;
    font-size: 1.2rem;
    margin-top: 1rem;
    text-align: center;
    text-shadow: 0 0 5px #000;
}

/* Recipe Info */
.recipe-info {
    margin-top: 2rem;
    text-align: center;
    background: rgba(0, 0, 0, 0.5);
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid #FFD700;
}

#recipe-name {
    font-family: 'MedievalSharp', cursive;
    font-size: 2rem;
    color: #FFD700;
    margin-bottom: 1rem;
}

#recipe-description {
    font-size: 1.2rem;
    color: #DDD;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

/* Tlačidlá */
.magic-button {
    background: linear-gradient(45deg, #8A2BE2, #FFD700);
    border: none;
    color: #191970;
    padding: 15px 30px;
    font-size: 1.2rem;
    font-family: 'Cinzel', serif;
    font-weight: 600;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    min-width: 150px;
    margin: 10px;
}

.magic-button:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
    background: linear-gradient(45deg, #FFD700, #8A2BE2);
}

.magic-button:active {
    transform: translateY(-1px) scale(1.02);
}

.magic-button.secondary {
    background: linear-gradient(45deg, #2F4F4F, #191970);
    color: #FFD700;
}

.magic-button.secondary:hover {
    background: linear-gradient(45deg, #191970, #2F4F4F);
}

/* Game Screen */
.game-header {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
}

.timer-container {
    display: flex;
    align-items: center;
}

.timer-circle {
    width: 80px;
    height: 80px;
    border: 4px solid #FFD700;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.7);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

#timer-text {
    font-size: 1.2rem;
    font-weight: bold;
    color: #FFD700;
}

.score-container {
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 20px;
    border-radius: 25px;
    border: 2px solid #FFD700;
}

.score-label {
    color: #DDD;
    margin-right: 10px;
}

#current-score {
    color: #FFD700;
    font-size: 1.5rem;
    font-weight: bold;
}

/* Game Content */
.game-content {
    width: 90%;
    max-width: 1200px;
    margin-top: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.recipe-progress {
    text-align: center;
    margin-bottom: 2rem;
}

#current-recipe-name {
    font-family: 'MedievalSharp', cursive;
    font-size: 2rem;
    color: #FFD700;
    margin-bottom: 1rem;
}

.step-indicator {
    font-size: 1.5rem;
    color: #DDD;
}

#current-step {
    color: #FFD700;
    font-weight: bold;
}

/* Current Step Container */
.current-step-container {
    display: flex;
    width: 100%;
    gap: 3rem;
    margin-bottom: 2rem;
}

.step-instruction {
    flex: 1;
    background: rgba(0, 0, 0, 0.5);
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid #FFD700;
}

#step-title {
    font-family: 'MedievalSharp', cursive;
    font-size: 1.8rem;
    color: #FFD700;
    margin-bottom: 1rem;
}

#step-description {
    font-size: 1.2rem;
    color: #DDD;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.step-hint {
    background: rgba(255, 215, 0, 0.1);
    padding: 1rem;
    border-radius: 10px;
    border-left: 4px solid #FFD700;
    font-style: italic;
    color: #FFD700;
}

.step-visual {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Status Panel */
.status-panel {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    padding: 15px;
    border-radius: 15px;
    border: 2px solid #FFD700;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bluetooth-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #FF0000;
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    background: #00FF00;
}

.sensor-readings {
    display: flex;
    gap: 2rem;
}

.sensor-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.sensor-label {
    color: #DDD;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: linear-gradient(135deg, #191970, #2F4F4F);
    padding: 2rem;
    border-radius: 20px;
    border: 3px solid #FFD700;
    text-align: center;
    max-width: 500px;
    box-shadow: 0 0 50px rgba(255, 215, 0, 0.3);
}

.modal-content h3 {
    color: #FFD700;
    font-family: 'MedievalSharp', cursive;
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

.modal-content p {
    color: #DDD;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}
