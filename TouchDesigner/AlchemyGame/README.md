# Čarodejnícka Alchymistická Hra
## Interaktívna hra pre zábavný park

Táto aplikácia obsahuje:
- **web-app/**: Webová aplikácia pre Windows tablet
- **raspberry-pi/**: Python kontrolér pre Raspberry Pi
- **documentation/**: Kompletná dokumentácia a návody

### Technológie
- Web: HTML5 + JavaScript + CSS3 + Web Bluetooth API
- Hardware: Raspberry Pi 4 + Python 3.8+
- Komunikácia: Bluetooth Low Energy (BLE)

### Inštalácia
Pozrite si dokumentáciu v priečinku `documentation/` pre detailné pokyny.

### Štruktúra projektu
```
AlchemyGame/
├── web-app/                    # Webová aplikácia pre tablet
│   ├── index.html             # Hlavná stránka
│   ├── css/                   # Štýly
│   ├── js/                    # JavaScript moduly
│   ├── assets/                # Médiá (video, audio, obrázky)
│   └── data/                  # Recepty a konfigurácia
├── raspberry-pi/              # Python kontrolér
│   ├── main.py               # Hlavná slučka
│   ├── bluetooth_web_server.py # BLE server
│   ├── sensor_manager.py     # Senzory
│   └── config.py             # Nastavenia
└── documentation/            # Dokumentácia
    ├── installation.md       # Inštalačný návod
    ├── wiring-diagram.md     # Zapojenie hardvéru
    └── troubleshooting.md    # Riešenie problémov
```

### Herný proces
1. Úvodné video (Merlin vysvetľuje úlohu)
2. Výber receptu pomocou QR kódu
3. Postupné odmeravanie ingrediencií
4. Miešanie čarodejníckou palicou
5. Vizuálne a zvukové efekty
6. Vyhodnotenie úspešnosti

### Časový limit
15 minút na dokončenie receptu

### Vekové zameranie
8-15 rokov
