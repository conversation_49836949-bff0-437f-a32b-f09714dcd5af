#!/usr/bin/env python3
"""
Blender Lip Sync Rigging Script
Automaticky nariguje postavu pre lip sync animáciu
"""

import bpy
import bmesh
import os
from mathutils import Vector, Matrix
import addon_utils

def setup_blender_scene():
    """Nastavenie Blender scény pre rigging"""
    print("🎬 Nastavujem Blender scénu...")
    
    # Vymazanie default objektov
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    # Pridanie svetla a kamery
    bpy.ops.object.light_add(type='SUN', location=(5, 5, 10))
    bpy.ops.object.camera_add(location=(0, -10, 5))
    
    # Nastavenie kamery
    camera = bpy.context.object
    camera.rotation_euler = (1.1, 0, 0)
    
    print("✅ Scéna pripravená")

def import_character_image():
    """Import obrázka postavy"""
    print("📷 Importujem obrázok postavy...")
    
    # Cesta k súboru
    image_path = os.path.expanduser("~/Downloads/Dizajn bez názvu.png")
    
    if not os.path.exists(image_path):
        print(f"❌ Súbor neexistuje: {image_path}")
        return None
    
    # Import ako reference image (opravená metóda)
    
    # Alebo vytvorenie plane s textúrou
    bpy.ops.mesh.primitive_plane_add(size=4, location=(0, 0, 0))
    plane = bpy.context.object
    plane.name = "Character_Base"
    
    # Vytvorenie materiálu
    mat = bpy.data.materials.new(name="Character_Material")
    mat.use_nodes = True
    plane.data.materials.append(mat)
    
    # Načítanie textúry
    nodes = mat.node_tree.nodes
    nodes.clear()
    
    # Shader setup
    output = nodes.new('ShaderNodeOutputMaterial')
    principled = nodes.new('ShaderNodeBsdfPrincipled')
    tex_image = nodes.new('ShaderNodeTexImage')
    
    # Načítanie obrázka
    try:
        img = bpy.data.images.load(image_path)
        tex_image.image = img
    except:
        print(f"❌ Chyba pri načítaní obrázka: {image_path}")
        return None
    
    # Prepojenie nodes
    mat.node_tree.links.new(tex_image.outputs['Color'], principled.inputs['Base Color'])
    mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    print("✅ Obrázok importovaný")
    return plane

def create_face_mesh():
    """Vytvorenie 3D mesh pre tvár"""
    print("🎭 Vytváram 3D mesh pre tvár...")
    
    # Vytvorenie základnej hlavy
    bpy.ops.mesh.primitive_uv_sphere_add(radius=1, location=(0, 0, 2))
    head = bpy.context.object
    head.name = "Head"
    
    # Editovanie mesh pre tvár
    bpy.context.view_layer.objects.active = head
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Vytvorenie základných oblastí tváre
    bpy.ops.mesh.select_all(action='DESELECT')
    
    # Výber prednej časti pre tvár
    bpy.ops.object.mode_set(mode='OBJECT')
    
    print("✅ 3D mesh vytvorený")
    return head

def create_mouth_bones():
    """Vytvorenie kostí pre ústa a lip sync"""
    print("👄 Vytváram kosti pre ústa...")
    
    # Vytvorenie armatúry
    bpy.ops.object.armature_add(location=(0, 0, 2))
    armature = bpy.context.object
    armature.name = "Face_Rig"
    
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Vymazanie default bone
    bpy.ops.armature.select_all(action='SELECT')
    bpy.ops.armature.delete()
    
    # Vytvorenie kostí pre lip sync
    mouth_bones = [
        ("Mouth_Master", (0, 0.1, 1.8)),
        ("Mouth_Corner_L", (-0.3, 0.1, 1.8)),
        ("Mouth_Corner_R", (0.3, 0.1, 1.8)),
        ("Mouth_Upper", (0, 0.1, 1.9)),
        ("Mouth_Lower", (0, 0.1, 1.7)),
        ("Jaw", (0, 0, 1.5)),
        
        # Viseme bones
        ("Viseme_A", (0, 0.1, 1.8)),
        ("Viseme_E", (0, 0.1, 1.8)),
        ("Viseme_I", (0, 0.1, 1.8)),
        ("Viseme_O", (0, 0.1, 1.8)),
        ("Viseme_U", (0, 0.1, 1.8)),
        ("Viseme_M", (0, 0.1, 1.8)),
        ("Viseme_F", (0, 0.1, 1.8)),
        ("Viseme_S", (0, 0.1, 1.8)),
        ("Viseme_T", (0, 0.1, 1.8)),
        ("Viseme_L", (0, 0.1, 1.8)),
        ("Viseme_R", (0, 0.1, 1.8)),
    ]
    
    bones = []
    for bone_name, location in mouth_bones:
        bpy.ops.armature.bone_primitive_add(name=bone_name)
        bone = armature.data.edit_bones[bone_name]
        bone.head = Vector(location)
        bone.tail = Vector((location[0], location[1] - 0.1, location[2]))
        bones.append(bone)
    
    # Nastavenie parent relationships
    jaw_bone = armature.data.edit_bones["Jaw"]
    mouth_master = armature.data.edit_bones["Mouth_Master"]
    mouth_master.parent = jaw_bone
    
    for bone_name, _ in mouth_bones[1:6]:  # Corner a Upper/Lower bones
        bone = armature.data.edit_bones[bone_name]
        bone.parent = mouth_master
    
    bpy.ops.object.mode_set(mode='OBJECT')
    
    print("✅ Kosti pre ústa vytvorené")
    return armature

def create_eye_bones(armature):
    """Vytvorenie kostí pre oči"""
    print("👁️ Vytváram kosti pre oči...")
    
    bpy.context.view_layer.objects.active = armature
    bpy.ops.object.mode_set(mode='EDIT')
    
    eye_bones = [
        ("Eye_L", (-0.4, 0.1, 2.2)),
        ("Eye_R", (0.4, 0.1, 2.2)),
        ("Eyelid_Upper_L", (-0.4, 0.1, 2.3)),
        ("Eyelid_Lower_L", (-0.4, 0.1, 2.1)),
        ("Eyelid_Upper_R", (0.4, 0.1, 2.3)),
        ("Eyelid_Lower_R", (0.4, 0.1, 2.1)),
        ("Eyebrow_L", (-0.4, 0.1, 2.4)),
        ("Eyebrow_R", (0.4, 0.1, 2.4)),
    ]
    
    for bone_name, location in eye_bones:
        bpy.ops.armature.bone_primitive_add(name=bone_name)
        bone = armature.data.edit_bones[bone_name]
        bone.head = Vector(location)
        bone.tail = Vector((location[0], location[1] - 0.05, location[2]))
    
    bpy.ops.object.mode_set(mode='OBJECT')
    
    print("✅ Kosti pre oči vytvorené")

def setup_shape_keys(mesh_obj):
    """Vytvorenie shape keys pre lip sync"""
    print("🔧 Vytváram shape keys...")
    
    bpy.context.view_layer.objects.active = mesh_obj
    
    # Basis shape key
    bpy.ops.object.shape_key_add(from_mix=False)
    mesh_obj.data.shape_keys.key_blocks[0].name = "Basis"
    
    # Viseme shape keys
    visemes = [
        "A", "E", "I", "O", "U", "M", "F", "S", "T", "L", "R",
        "Smile", "Frown", "Surprised", "Angry", "Blink_L", "Blink_R"
    ]
    
    for viseme in visemes:
        bpy.ops.object.shape_key_add(from_mix=False)
        shape_key = mesh_obj.data.shape_keys.key_blocks[-1]
        shape_key.name = f"Viseme_{viseme}"
        shape_key.value = 0.0
    
    print("✅ Shape keys vytvorené")

def setup_drivers(mesh_obj, armature):
    """Nastavenie drivers pre automatický lip sync"""
    print("🎛️ Nastavujem drivers...")
    
    if not mesh_obj.data.shape_keys:
        print("❌ Žiadne shape keys na nastavenie drivers")
        return
    
    shape_keys = mesh_obj.data.shape_keys.key_blocks
    
    # Vytvorenie drivers pre každý viseme
    viseme_bones = [
        "Viseme_A", "Viseme_E", "Viseme_I", "Viseme_O", "Viseme_U",
        "Viseme_M", "Viseme_F", "Viseme_S", "Viseme_T", "Viseme_L", "Viseme_R"
    ]
    
    for i, bone_name in enumerate(viseme_bones):
        if bone_name.replace("Viseme_", "Viseme_") in [sk.name for sk in shape_keys]:
            shape_key = shape_keys[f"Viseme_{bone_name.split('_')[1]}"]
            
            # Vytvorenie driver
            driver = shape_key.driver_add("value")
            driver.driver.type = 'AVERAGE'
            
            # Pridanie variable
            var = driver.driver.variables.new()
            var.name = "bone_influence"
            var.type = 'TRANSFORMS'
            
            target = var.targets[0]
            target.id = armature
            target.bone_target = bone_name
            target.transform_type = 'LOC_Z'
            target.transform_space = 'LOCAL_SPACE'
    
    print("✅ Drivers nastavené")

def setup_constraints(armature):
    """Nastavenie constraints pre realistickejšie pohyby"""
    print("🔗 Nastavujem constraints...")
    
    bpy.context.view_layer.objects.active = armature
    bpy.ops.object.mode_set(mode='POSE')
    
    # Limit rotation pre jaw
    if "Jaw" in armature.pose.bones:
        jaw_bone = armature.pose.bones["Jaw"]
        constraint = jaw_bone.constraints.new('LIMIT_ROTATION')
        constraint.use_limit_x = True
        constraint.min_x = -0.5
        constraint.max_x = 0.1
        constraint.owner_space = 'LOCAL'
    
    bpy.ops.object.mode_set(mode='OBJECT')
    
    print("✅ Constraints nastavené")

def create_lip_sync_controls():
    """Vytvorenie custom properties pre lip sync ovládanie"""
    print("🎚️ Vytváram lip sync controls...")
    
    # Vytvorenie empty objektu pre controls
    bpy.ops.object.empty_add(type='PLAIN_AXES', location=(3, 0, 2))
    controller = bpy.context.object
    controller.name = "LipSync_Controller"
    
    # Pridanie custom properties
    visemes = ["A", "E", "I", "O", "U", "M", "F", "S", "T", "L", "R"]
    
    for viseme in visemes:
        controller[f"Viseme_{viseme}"] = 0.0
        
        # Nastavenie min/max hodnôt
        controller_rna = controller.id_properties_ui(f"Viseme_{viseme}")
        controller_rna.update(min=0.0, max=1.0, soft_min=0.0, soft_max=1.0)
    
    # Pridanie master controls
    controller["Mouth_Open"] = 0.0
    controller["Jaw_Open"] = 0.0
    controller["Smile"] = 0.0
    
    for prop in ["Mouth_Open", "Jaw_Open", "Smile"]:
        controller_rna = controller.id_properties_ui(prop)
        controller_rna.update(min=0.0, max=1.0, soft_min=0.0, soft_max=1.0)
    
    print("✅ Lip sync controls vytvorené")
    return controller

def setup_audio_import():
    """Príprava na import audio súboru"""
    print("🎵 Pripravujem audio import...")
    
    # Nastavenie frame rate
    bpy.context.scene.render.fps = 24
    bpy.context.scene.frame_start = 1
    bpy.context.scene.frame_end = 250
    
    # Vytvorenie markera pre audio sync
    bpy.context.scene.timeline_markers.new("Audio_Start", frame=1)
    
    print("✅ Audio import pripravený")

def create_complete_rig():
    """Hlavná funkcia - vytvorenie kompletného rigu"""
    print("🎭 BLENDER LIP SYNC RIG - AUTOMATICKÉ VYTVORENIE")
    print("=" * 50)
    
    # 1. Nastavenie scény
    setup_blender_scene()
    
    # 2. Import postavy
    character_plane = import_character_image()
    if not character_plane:
        print("❌ Chyba pri importe postavy")
        return
    
    # 3. Vytvorenie 3D mesh
    head_mesh = create_face_mesh()
    
    # 4. Vytvorenie kostí
    armature = create_mouth_bones()
    create_eye_bones(armature)
    
    # 5. Shape keys
    setup_shape_keys(head_mesh)
    
    # 6. Drivers
    setup_drivers(head_mesh, armature)
    
    # 7. Constraints
    setup_constraints(armature)
    
    # 8. Controls
    controller = create_lip_sync_controls()
    
    # 9. Audio príprava
    setup_audio_import()
    
    # 10. Finálne nastavenia
    bpy.context.view_layer.objects.active = armature
    bpy.ops.object.select_all(action='DESELECT')
    armature.select_set(True)
    head_mesh.select_set(True)
    
    # Parent mesh to armature
    bpy.ops.object.parent_set(type='ARMATURE_AUTO')
    
    print("\n" + "=" * 50)
    print("🎉 ÚSPECH! Lip sync rig vytvorený!")
    print("\n📋 Čo je pripravené:")
    print("   • Face_Rig - Armatúra s kosťami pre lip sync")
    print("   • Head - 3D mesh s shape keys")
    print("   • LipSync_Controller - Ovládacie prvky")
    print("   • Character_Base - 2D referenčný obrázok")
    print("\n🎵 Pripravené na import MP3:")
    print("   • Sequencer > Add > Sound > Váš MP3 súbor")
    print("   • Použite Viseme_* properties na animáciu")
    print("\n🎬 Ďalšie kroky:")
    print("   1. Importujte MP3 súbor")
    print("   2. Analyzujte audio pre visemes")
    print("   3. Animujte lip sync pomocou keyframes")

def import_audio_file(audio_path):
    """Import MP3 súboru do Blender sequencera"""
    print(f"🎵 Importujem audio: {audio_path}")

    if not os.path.exists(audio_path):
        print(f"❌ Audio súbor neexistuje: {audio_path}")
        return False

    # Prepnutie na Video Sequence Editor
    for area in bpy.context.screen.areas:
        if area.type == 'SEQUENCE_EDITOR':
            break
    else:
        # Vytvorenie nového workspace pre sequencer
        bpy.ops.workspace.append_activate(idname="Video Editing")

    # Import audio
    bpy.ops.sequencer.sound_strip_add(filepath=audio_path, frame_start=1)

    # Nastavenie dĺžky animácie podľa audio
    for strip in bpy.context.scene.sequence_editor.sequences:
        if strip.type == 'SOUND':
            bpy.context.scene.frame_end = strip.frame_final_end
            break

    print("✅ Audio importované")
    return True

def analyze_audio_for_visemes(audio_path):
    """Analýza audio súboru pre automatické generovanie visemes"""
    print("🔍 Analyzujem audio pre visemes...")

    try:
        import librosa
        import numpy as np
    except ImportError:
        print("📦 Inštalujem potrebné knižnice...")
        import subprocess
        subprocess.check_call([bpy.app.binary_path_python, "-m", "pip", "install", "librosa"])
        import librosa
        import numpy as np

    # Načítanie audio súboru
    y, sr = librosa.load(audio_path)

    # Analýza spektra pre detekciu visemes
    hop_length = 512
    frame_length = 2048

    # Extrakcia MFCC features
    mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13, hop_length=hop_length)

    # Detekcia energie pre určenie kedy sa hovorí
    energy = librosa.feature.rms(y=y, hop_length=hop_length)[0]

    # Konverzia na frame čísla pre Blender
    fps = bpy.context.scene.render.fps
    frames_per_hop = hop_length / sr * fps

    viseme_data = []

    for i, (mfcc_frame, energy_frame) in enumerate(zip(mfccs.T, energy)):
        frame_number = int(i * frames_per_hop) + 1

        # Jednoduchá klasifikácia visemes na základe MFCC
        if energy_frame > 0.01:  # Threshold pre detekciu reči
            # Klasifikácia na základe spektrálnych charakteristík
            if mfcc_frame[1] > 0:  # Vysoké frekvencie
                if mfcc_frame[2] > 0:
                    viseme = "E"  # Široké ústa
                else:
                    viseme = "I"  # Úzke ústa
            elif mfcc_frame[1] < -5:  # Nízke frekvencie
                if mfcc_frame[3] > 0:
                    viseme = "O"  # Okrúhle ústa
                else:
                    viseme = "U"  # Malé okrúhle ústa
            else:  # Stredné frekvencie
                if energy_frame > 0.05:
                    viseme = "A"  # Otvorené ústa
                else:
                    viseme = "M"  # Zatvorené ústa
        else:
            viseme = "Rest"  # Ticho

        viseme_data.append((frame_number, viseme))

    print(f"✅ Analyzovaných {len(viseme_data)} frames")
    return viseme_data

def animate_lip_sync(viseme_data, controller):
    """Vytvorenie keyframes pre lip sync animáciu"""
    print("🎬 Vytváram lip sync animáciu...")

    # Vymazanie existujúcich keyframes
    controller.animation_data_clear()

    visemes = ["A", "E", "I", "O", "U", "M", "F", "S", "T", "L", "R"]

    for frame, viseme in viseme_data:
        bpy.context.scene.frame_set(frame)

        # Reset všetkých visemes
        for v in visemes:
            controller[f"Viseme_{v}"] = 0.0
            controller.keyframe_insert(data_path=f'["Viseme_{v}"]', frame=frame)

        # Nastavenie aktívneho viseme
        if viseme in visemes:
            controller[f"Viseme_{viseme}"] = 1.0
            controller.keyframe_insert(data_path=f'["Viseme_{viseme}"]', frame=frame)

        # Nastavenie jaw opening pre otvorené visemes
        if viseme in ["A", "E", "O"]:
            controller["Jaw_Open"] = 0.5
        elif viseme in ["I", "U"]:
            controller["Jaw_Open"] = 0.2
        else:
            controller["Jaw_Open"] = 0.0

        controller.keyframe_insert(data_path='["Jaw_Open"]', frame=frame)

    print("✅ Lip sync animácia vytvorená")

def setup_auto_lipsync(audio_path):
    """Kompletné nastavenie automatického lip sync"""
    print("🤖 Nastavujem automatický lip sync...")

    # Import audio
    if not import_audio_file(audio_path):
        return False

    # Analýza audio
    viseme_data = analyze_audio_for_visemes(audio_path)

    # Nájdenie controllera
    controller = bpy.data.objects.get("LipSync_Controller")
    if not controller:
        print("❌ LipSync_Controller nenájdený")
        return False

    # Vytvorenie animácie
    animate_lip_sync(viseme_data, controller)

    print("✅ Automatický lip sync nastavený")
    return True

if __name__ == "__main__":
    create_complete_rig()
