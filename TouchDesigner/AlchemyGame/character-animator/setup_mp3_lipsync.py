#!/usr/bin/env python3
"""
Script pre nastavenie MP3 lip sync v Blenderi
Použitie: python3 setup_mp3_lipsync.py "cesta/k/audio.mp3"
"""

import sys
import os
import subprocess

def setup_mp3_lipsync(mp3_path):
    """Nastavenie lip sync s MP3 súborom"""
    
    if not os.path.exists(mp3_path):
        print(f"❌ MP3 súbor neexistuje: {mp3_path}")
        return False
    
    print(f"🎵 Nastavujem lip sync pre: {mp3_path}")
    
    # Blender script pre import MP3
    blender_script = f'''
import bpy
import os

def import_and_setup_audio():
    """Import MP3 a nastavenie lip sync"""
    
    audio_path = r"{mp3_path}"
    
    print(f"🎵 Importujem audio: {{audio_path}}")
    
    # Vytvorenie sequence editora ak neexistuje
    if not bpy.context.scene.sequence_editor:
        bpy.context.scene.sequence_editor_create()
    
    # Import audio
    try:
        bpy.ops.sequencer.sound_strip_add(filepath=audio_path, frame_start=1)
        print("✅ Audio importované")
        
        # Nastavenie dĺžky animácie
        for strip in bpy.context.scene.sequence_editor.sequences:
            if strip.type == 'SOUND':
                bpy.context.scene.frame_end = strip.frame_final_end
                print(f"📏 Dĺžka animácie nastavená na {{strip.frame_final_end}} frames")
                break
        
        # Nájdenie controllera
        controller = bpy.data.objects.get("LipSync_Controller")
        if controller:
            print("✅ LipSync_Controller nájdený")
            
            # Vytvorenie základných keyframes
            create_basic_lipsync_animation(controller)
            
        else:
            print("❌ LipSync_Controller nenájdený")
            
    except Exception as e:
        print(f"❌ Chyba pri importe audio: {{e}}")
        return False
    
    return True

def create_basic_lipsync_animation(controller):
    """Vytvorenie základnej lip sync animácie"""
    print("🎬 Vytváram základnú lip sync animáciu...")
    
    # Vymazanie existujúcich keyframes
    if controller.animation_data:
        controller.animation_data_clear()
    
    # Základné visemes pre demo
    visemes = ["A", "E", "I", "O", "U", "M"]
    frame_duration = 10  # frames na viseme
    
    for i, viseme in enumerate(visemes):
        frame = 1 + (i * frame_duration)
        
        # Reset všetkých visemes
        for v in ["A", "E", "I", "O", "U", "M", "F", "S", "T", "L", "R"]:
            controller[f"Viseme_{{v}}"] = 0.0
            controller.keyframe_insert(data_path=f'["Viseme_{{v}}"]', frame=frame)
        
        # Nastavenie aktívneho viseme
        controller[f"Viseme_{{viseme}}"] = 1.0
        controller.keyframe_insert(data_path=f'["Viseme_{{viseme}}"]', frame=frame)
        
        # Jaw opening
        if viseme in ["A", "E", "O"]:
            controller["Jaw_Open"] = 0.6
        elif viseme in ["I", "U"]:
            controller["Jaw_Open"] = 0.3
        else:
            controller["Jaw_Open"] = 0.0
        
        controller.keyframe_insert(data_path='["Jaw_Open"]', frame=frame)
        
        print(f"  📍 Keyframe {{frame}}: Viseme_{{viseme}}")
    
    print("✅ Základná animácia vytvorená")

# Spustenie funkcie
if import_and_setup_audio():
    # Uloženie súboru
    blend_path = r"{os.path.join(os.path.dirname(__file__), 'character_with_lipsync.blend')}"
    bpy.ops.wm.save_as_mainfile(filepath=blend_path)
    print(f"💾 Súbor uložený: {{blend_path}}")
    print("🎉 Lip sync hotový!")
else:
    print("❌ Chyba pri nastavovaní lip sync")
'''
    
    # Uloženie Blender scriptu
    script_path = os.path.join(os.path.dirname(__file__), "temp_audio_setup.py")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(blender_script)
    
    # Spustenie Blender s scriptom
    blender_path = "/Applications/Blender.app/Contents/MacOS/Blender"
    blend_file = os.path.join(os.path.dirname(__file__), "character_lipsync_rig.blend")
    
    if not os.path.exists(blend_file):
        print(f"❌ Blend súbor neexistuje: {blend_file}")
        return False
    
    try:
        cmd = [
            blender_path,
            blend_file,
            "--python", script_path
        ]
        
        print("🎬 Spúšťam Blender s MP3 setupom...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ MP3 lip sync úspešne nastavený!")
            print("📁 Súbor: character_with_lipsync.blend")
            
            # Vymazanie temp scriptu
            os.remove(script_path)
            return True
        else:
            print(f"❌ Chyba pri nastavovaní lip sync:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return False

def main():
    """Hlavná funkcia"""
    if len(sys.argv) != 2:
        print("🎵 BLENDER MP3 LIP SYNC SETUP")
        print("=" * 30)
        print("Použitie:")
        print(f"  python3 {sys.argv[0]} 'cesta/k/audio.mp3'")
        print("\nPríklad:")
        print(f"  python3 {sys.argv[0]} '~/Downloads/moja_rec.mp3'")
        return
    
    mp3_path = os.path.expanduser(sys.argv[1])
    
    print("🎵 BLENDER MP3 LIP SYNC SETUP")
    print("=" * 30)
    print(f"📁 MP3 súbor: {mp3_path}")
    
    if setup_mp3_lipsync(mp3_path):
        print("\n🎉 ÚSPECH!")
        print("📋 Čo je hotové:")
        print("   • MP3 importované do Blender")
        print("   • Základná lip sync animácia vytvorená")
        print("   • Súbor uložený ako character_with_lipsync.blend")
        print("\n🎬 Ďalšie kroky:")
        print("   1. Otvorte character_with_lipsync.blend v Blenderi")
        print("   2. Stlačte Spacebar pre prehratie animácie")
        print("   3. Upravte keyframes podľa potreby")
        print("   4. Renderujte finálne video")
    else:
        print("\n❌ Chyba pri nastavovaní lip sync")

if __name__ == "__main__":
    main()
