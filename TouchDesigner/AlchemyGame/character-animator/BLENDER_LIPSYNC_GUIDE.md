# 🎭 Blender Lip Sync Rigging - Kompletný návod

## 🎯 Čo vytvorím

Automaticky narigujem vašu postavu "Dizajn bez názvu" v Blenderi s:
- ✅ **Kompletný face rig** pre lip sync
- ✅ **Automatická analýza MP3** a generovanie animácie
- ✅ **Viseme bones** pre všetky zvuky
- ✅ **Shape keys** pre facial expressions
- ✅ **Controllers** pre manuálne ovládanie

## 🚀 Rýchly štart (2 spôsoby)

### **Spôsob 1: Automatick<PERSON> (Odporúčané)**
```bash
cd TouchDesigner/AlchemyGame/character-animator
python3 run_blender_rig.py
```

### **Spôsob 2: Manuálny**
```bash
# 1. Otvorte Blender
# 2. File > Open > blender_lipsync_rig.py
# 3. Spustite script (Alt+P)
```

## 📋 Krok za krokom

### Krok 1: Pr<PERSON>pra<PERSON>
```bash
# Skontrolujte že máte:
✅ Blen<PERSON> na<PERSON>ný
✅ "Dizajn bez názvu.png" v Downloads
✅ Python scripty v character-animator priečinku
```

### Krok 2: Spustenie rigging scriptu
```bash
python3 run_blender_rig.py
```

**Čo sa stane:**
- Blender sa otvorí
- Importuje sa váš obrázok
- Vytvorí sa 3D mesh
- Pridajú sa kosti pre lip sync
- Nastavia sa shape keys a drivers

### Krok 3: Import MP3 (keď budete mať audio)
```python
# V Blender Python console:
exec(open("blender_lipsync_rig.py").read())
setup_auto_lipsync("cesta/k/vasmu/audio.mp3")
```

## 🎛️ Čo bude vytvorené

### **Objekty v scéne:**
```
📁 Scene Collection
├── 🎭 Character_Base (2D referenčný obrázok)
├── 🦴 Face_Rig (Armatúra s kosťami)
├── 👤 Head (3D mesh s shape keys)
├── 🎚️ LipSync_Controller (Ovládacie prvky)
├── 💡 Sun (Svetlo)
└── 📷 Camera
```

### **Kosti v Face_Rig:**
```
🦴 Face_Rig
├── Jaw (Hlavná čeľusť)
├── Mouth_Master (Hlavné ústa)
│   ├── Mouth_Corner_L (Ľavý kútik)
│   ├── Mouth_Corner_R (Pravý kútik)
│   ├── Mouth_Upper (Horná pera)
│   └── Mouth_Lower (Spodná pera)
├── Eye_L / Eye_R (Oči)
├── Eyelid_Upper_L/R (Horné viečka)
├── Eyelid_Lower_L/R (Spodné viečka)
├── Eyebrow_L/R (Obočie)
└── Viseme_* (11 kostí pre lip sync)
    ├── Viseme_A, Viseme_E, Viseme_I
    ├── Viseme_O, Viseme_U, Viseme_M
    ├── Viseme_F, Viseme_S, Viseme_T
    └── Viseme_L, Viseme_R
```

### **Shape Keys na Head mesh:**
```
🎭 Shape Keys
├── Basis (Základný tvar)
├── Viseme_A (Otvorené ústa)
├── Viseme_E (Široké ústa)
├── Viseme_I (Úzke ústa)
├── Viseme_O (Okrúhle ústa)
├── Viseme_U (Malé okrúhle ústa)
├── Viseme_M (Zatvorené ústa)
├── Viseme_F (F/V zvuky)
├── Viseme_S (S/T zvuky)
├── Viseme_L (L zvuk)
├── Viseme_R (R zvuk)
├── Smile (Úsmev)
├── Frown (Smutný výraz)
├── Surprised (Prekvapenie)
├── Angry (Hnev)
├── Blink_L (Ľavé oko zatvorené)
└── Blink_R (Pravé oko zatvorené)
```

### **LipSync_Controller properties:**
```
🎚️ Ovládacie prvky (0.0 - 1.0)
├── Viseme_A, Viseme_E, Viseme_I
├── Viseme_O, Viseme_U, Viseme_M
├── Viseme_F, Viseme_S, Viseme_T
├── Viseme_L, Viseme_R
├── Mouth_Open (Všeobecné otvorenie úst)
├── Jaw_Open (Otvorenie čeľuste)
└── Smile (Úsmev)
```

## 🎵 Práca s MP3 súborom

### Automatická analýza:
```python
# Spustite v Blender:
setup_auto_lipsync("path/to/your/audio.mp3")
```

**Čo sa stane:**
1. MP3 sa importuje do Video Sequence Editor
2. Audio sa analyzuje pomocou librosa
3. Automaticky sa detekujú visemes
4. Vytvorí sa keyframe animácia
5. Lip sync je pripravený na prehratie

### Manuálne nastavenie:
```python
# Frame 1
controller["Viseme_A"] = 1.0
controller.keyframe_insert(data_path='["Viseme_A"]', frame=1)

# Frame 10
controller["Viseme_A"] = 0.0
controller["Viseme_E"] = 1.0
controller.keyframe_insert(data_path='["Viseme_A"]', frame=10)
controller.keyframe_insert(data_path='["Viseme_E"]', frame=10)
```

## 🎬 Ovládanie animácie

### Prehrávanie:
```
Spacebar - Play/Pause
Left/Right arrows - Frame by frame
Shift+Left/Right - Jump 10 frames
```

### Keyframe editing:
```
I - Insert keyframe
X - Delete keyframe
G - Grab/Move keyframe
S - Scale keyframes
```

### Timeline:
```
Timeline - Základné keyframes
Dope Sheet - Detailné editovanie
Graph Editor - Curves a interpolácia
```

## 🔧 Customizácia

### Úprava visemes:
```python
# Pridanie nového viseme
bpy.ops.object.shape_key_add(from_mix=False)
shape_key = mesh_obj.data.shape_keys.key_blocks[-1]
shape_key.name = "Viseme_Custom"
```

### Úprava kostí:
```python
# V Edit mode armatúry
bpy.ops.armature.bone_primitive_add(name="Custom_Bone")
bone = armature.data.edit_bones["Custom_Bone"]
bone.head = Vector((x, y, z))
bone.tail = Vector((x, y-0.1, z))
```

### Úprava drivers:
```python
# Vytvorenie custom driver
driver = shape_key.driver_add("value")
driver.driver.expression = "var * 2"  # Custom formula
```

## 📊 Výkon a optimalizácia

### Pre lepší výkon:
- Znížte počet keyframes
- Použite linear interpoláciu
- Optimalizujte mesh topology
- Používajte proxy objekty pre preview

### Pre lepšiu kvalitu:
- Pridajte viac shape keys
- Jemnejšie keyframe spacing
- Smooth interpolácia
- Detailnejšie bone rigging

## 🎯 Výsledok

Po dokončení budete mať:
- ✅ **Plne funkčný lip sync rig**
- ✅ **Automatickú animáciu z MP3**
- ✅ **Manuálne ovládanie**
- ✅ **Profesionálne výsledky**

## 🚀 Ďalšie kroky

1. **Testovanie** - Prehratie animácie
2. **Fine-tuning** - Úprava keyframes
3. **Rendering** - Export videa
4. **Export** - Pre iné aplikácie

**Váš character je pripravený na profesionálnu lip sync animáciu!** 🎭✨
