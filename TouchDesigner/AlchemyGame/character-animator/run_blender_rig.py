#!/usr/bin/env python3
"""
Spúš<PERSON><PERSON><PERSON> script pre Blender lip sync rigging
Automaticky otvorí Blender a spustí rigging script
"""

import os
import subprocess
import sys

def find_blender_executable():
    """Nájde Blender executable na systéme"""
    possible_paths = [
        "/Applications/Blender.app/Contents/MacOS/Blender",  # macOS
        "/usr/bin/blender",  # Linux
        "/usr/local/bin/blender",  # Linux
        "C:\\Program Files\\Blender Foundation\\Blender\\blender.exe",  # Windows
        "C:\\Program Files (x86)\\Blender Foundation\\Blender\\blender.exe",  # Windows
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    # Skúsiť nájsť v PATH
    try:
        result = subprocess.run(["which", "blender"], capture_output=True, text=True)
        if result.returncode == 0:
            return result.stdout.strip()
    except:
        pass
    
    return None

def run_blender_with_script():
    """Spustí Blender s rigging scriptom"""
    print("🎭 BLENDER LIP SYNC RIGGING")
    print("=" * 40)
    
    # Nájdenie Blender
    blender_path = find_blender_executable()
    if not blender_path:
        print("❌ Blender nenájdený!")
        print("📋 Možné riešenia:")
        print("   1. Nainštalujte Blender z https://www.blender.org/")
        print("   2. Pridajte Blender do PATH")
        print("   3. Upravte cesty v tomto scripte")
        return False
    
    print(f"✅ Blender nájdený: {blender_path}")
    
    # Cesta k rigging scriptu
    script_path = os.path.join(os.path.dirname(__file__), "blender_lipsync_rig.py")
    
    if not os.path.exists(script_path):
        print(f"❌ Rigging script nenájdený: {script_path}")
        return False
    
    print(f"✅ Script nájdený: {script_path}")
    
    # Spustenie Blender s scriptom
    print("🚀 Spúšťam Blender...")
    
    try:
        # Blender command line argumenty
        cmd = [
            blender_path,
            "--background",  # Bez GUI (môžete odstrániť pre GUI)
            "--python", script_path
        ]
        
        # Pre GUI verziu (odkomentujte ak chcete vidieť Blender interface)
        cmd_gui = [
            blender_path,
            "--python", script_path
        ]
        
        print("🎬 Vytváram lip sync rig...")
        print("   (Toto môže trvať niekoľko minút)")
        
        # Spustenie (použite cmd_gui pre GUI verziu)
        result = subprocess.run(cmd_gui, capture_output=False)
        
        if result.returncode == 0:
            print("✅ Rig úspešne vytvorený!")
            return True
        else:
            print(f"❌ Chyba pri vytváraní rigu (exit code: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ Chyba pri spúšťaní Blender: {e}")
        return False

def setup_audio_lipsync(mp3_path):
    """Nastavenie lip sync s MP3 súborom"""
    print(f"🎵 Nastavujem lip sync pre: {mp3_path}")
    
    if not os.path.exists(mp3_path):
        print(f"❌ MP3 súbor neexistuje: {mp3_path}")
        return False
    
    # Vytvorenie script pre audio import
    audio_script = f"""
import bpy
import sys
import os

# Pridanie cesty k našim funkciám
sys.path.append(r"{os.path.dirname(__file__)}")

# Import našich funkcií
exec(open(r"{os.path.join(os.path.dirname(__file__), 'blender_lipsync_rig.py')}").read())

# Nastavenie automatického lip sync
setup_auto_lipsync(r"{mp3_path}")

# Uloženie súboru
bpy.ops.wm.save_as_mainfile(filepath=r"{os.path.join(os.path.dirname(__file__), 'character_with_lipsync.blend')}")

print("🎉 Lip sync hotový! Súbor uložený ako character_with_lipsync.blend")
"""
    
    # Uloženie audio scriptu
    audio_script_path = os.path.join(os.path.dirname(__file__), "setup_audio.py")
    with open(audio_script_path, 'w') as f:
        f.write(audio_script)
    
    # Spustenie Blender s audio scriptom
    blender_path = find_blender_executable()
    if not blender_path:
        print("❌ Blender nenájdený!")
        return False
    
    try:
        cmd = [
            blender_path,
            "--python", audio_script_path
        ]
        
        print("🎬 Vytváram lip sync animáciu...")
        result = subprocess.run(cmd, capture_output=False)
        
        if result.returncode == 0:
            print("✅ Lip sync animácia vytvorená!")
            print(f"📁 Súbor uložený: character_with_lipsync.blend")
            return True
        else:
            print(f"❌ Chyba pri vytváraní lip sync (exit code: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return False

def main():
    """Hlavná funkcia"""
    print("🎭 BLENDER LIP SYNC SETUP")
    print("=" * 30)
    
    # Krok 1: Vytvorenie základného rigu
    print("📋 Krok 1: Vytvorenie základného rigu")
    if not run_blender_with_script():
        print("❌ Chyba pri vytváraní rigu")
        return
    
    print("\n✅ Základný rig vytvorený!")
    print("\n📋 Ďalšie kroky:")
    print("   1. Otvorte Blender")
    print("   2. Načítajte vytvorený súbor")
    print("   3. Spustite setup_audio_lipsync() s vašim MP3")
    
    # Čakanie na MP3 súbor od používateľa
    print("\n🎵 Pripravený na MP3 súbor!")
    print("   Zavolajte setup_audio_lipsync('cesta/k/vasmu/audio.mp3')")

if __name__ == "__main__":
    main()
