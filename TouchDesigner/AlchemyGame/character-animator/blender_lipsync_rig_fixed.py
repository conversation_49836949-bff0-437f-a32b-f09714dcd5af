#!/usr/bin/env python3
"""
Blender Lip Sync Rigging Script - OPRAVENÁ VERZIA
Automaticky nariguje postavu pre lip sync animáciu
"""

import bpy
import bmesh
import os
from mathutils import Vector, Matrix

def setup_blender_scene():
    """Nastavenie Blender scény pre rigging"""
    print("🎬 Nastavujem Blender scénu...")
    
    # Vymazanie default objektov
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    # Pridanie svetla a kamery
    bpy.ops.object.light_add(type='SUN', location=(5, 5, 10))
    bpy.ops.object.camera_add(location=(0, -10, 5))
    
    # Nastavenie kamery
    camera = bpy.context.object
    camera.rotation_euler = (1.1, 0, 0)
    
    print("✅ Scéna pripravená")

def import_character_image():
    """Import obrázka postavy"""
    print("📷 Importujem obrázok postavy...")
    
    # Cesta k súboru
    image_path = os.path.expanduser("~/Downloads/Dizajn bez názvu.png")
    
    if not os.path.exists(image_path):
        print(f"❌ Súbor neexistuje: {image_path}")
        return None
    
    # Vytvorenie plane s textúrou
    bpy.ops.mesh.primitive_plane_add(size=4, location=(0, 0, 0))
    plane = bpy.context.object
    plane.name = "Character_Base"
    
    # Vytvorenie materiálu
    mat = bpy.data.materials.new(name="Character_Material")
    mat.use_nodes = True
    plane.data.materials.append(mat)
    
    # Načítanie textúry
    nodes = mat.node_tree.nodes
    nodes.clear()
    
    # Shader setup
    output = nodes.new('ShaderNodeOutputMaterial')
    principled = nodes.new('ShaderNodeBsdfPrincipled')
    tex_image = nodes.new('ShaderNodeTexImage')
    
    # Načítanie obrázka
    try:
        img = bpy.data.images.load(image_path)
        tex_image.image = img
        print(f"✅ Obrázok načítaný: {img.name}")
    except Exception as e:
        print(f"❌ Chyba pri načítaní obrázka: {e}")
        return None
    
    # Prepojenie nodes
    mat.node_tree.links.new(tex_image.outputs['Color'], principled.inputs['Base Color'])
    mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    print("✅ Obrázok importovaný")
    return plane

def create_face_mesh():
    """Vytvorenie 3D mesh pre tvár"""
    print("🎭 Vytváram 3D mesh pre tvár...")
    
    # Vytvorenie základnej hlavy
    bpy.ops.mesh.primitive_uv_sphere_add(radius=1, location=(0, 0, 2))
    head = bpy.context.object
    head.name = "Head"
    
    print("✅ 3D mesh vytvorený")
    return head

def create_mouth_bones():
    """Vytvorenie kostí pre ústa a lip sync"""
    print("👄 Vytváram kosti pre ústa...")
    
    # Vytvorenie armatúry
    bpy.ops.object.armature_add(location=(0, 0, 2))
    armature = bpy.context.object
    armature.name = "Face_Rig"
    
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Vymazanie default bone
    bpy.ops.armature.select_all(action='SELECT')
    bpy.ops.armature.delete()
    
    # Vytvorenie kostí pre lip sync
    mouth_bones = [
        ("Mouth_Master", (0, 0.1, 1.8)),
        ("Mouth_Corner_L", (-0.3, 0.1, 1.8)),
        ("Mouth_Corner_R", (0.3, 0.1, 1.8)),
        ("Mouth_Upper", (0, 0.1, 1.9)),
        ("Mouth_Lower", (0, 0.1, 1.7)),
        ("Jaw", (0, 0, 1.5)),
        
        # Viseme bones
        ("Viseme_A", (0, 0.1, 1.8)),
        ("Viseme_E", (0, 0.1, 1.8)),
        ("Viseme_I", (0, 0.1, 1.8)),
        ("Viseme_O", (0, 0.1, 1.8)),
        ("Viseme_U", (0, 0.1, 1.8)),
        ("Viseme_M", (0, 0.1, 1.8)),
        ("Viseme_F", (0, 0.1, 1.8)),
        ("Viseme_S", (0, 0.1, 1.8)),
        ("Viseme_T", (0, 0.1, 1.8)),
        ("Viseme_L", (0, 0.1, 1.8)),
        ("Viseme_R", (0, 0.1, 1.8)),
    ]
    
    for bone_name, location in mouth_bones:
        bpy.ops.armature.bone_primitive_add(name=bone_name)
        bone = armature.data.edit_bones[bone_name]
        bone.head = Vector(location)
        bone.tail = Vector((location[0], location[1] - 0.1, location[2]))
    
    # Nastavenie parent relationships
    jaw_bone = armature.data.edit_bones["Jaw"]
    mouth_master = armature.data.edit_bones["Mouth_Master"]
    mouth_master.parent = jaw_bone
    
    for bone_name, _ in mouth_bones[1:6]:  # Corner a Upper/Lower bones
        bone = armature.data.edit_bones[bone_name]
        bone.parent = mouth_master
    
    bpy.ops.object.mode_set(mode='OBJECT')
    
    print("✅ Kosti pre ústa vytvorené")
    return armature

def create_eye_bones(armature):
    """Vytvorenie kostí pre oči"""
    print("👁️ Vytváram kosti pre oči...")
    
    bpy.context.view_layer.objects.active = armature
    bpy.ops.object.mode_set(mode='EDIT')
    
    eye_bones = [
        ("Eye_L", (-0.4, 0.1, 2.2)),
        ("Eye_R", (0.4, 0.1, 2.2)),
        ("Eyelid_Upper_L", (-0.4, 0.1, 2.3)),
        ("Eyelid_Lower_L", (-0.4, 0.1, 2.1)),
        ("Eyelid_Upper_R", (0.4, 0.1, 2.3)),
        ("Eyelid_Lower_R", (0.4, 0.1, 2.1)),
        ("Eyebrow_L", (-0.4, 0.1, 2.4)),
        ("Eyebrow_R", (0.4, 0.1, 2.4)),
    ]
    
    for bone_name, location in eye_bones:
        bpy.ops.armature.bone_primitive_add(name=bone_name)
        bone = armature.data.edit_bones[bone_name]
        bone.head = Vector(location)
        bone.tail = Vector((location[0], location[1] - 0.05, location[2]))
    
    bpy.ops.object.mode_set(mode='OBJECT')
    
    print("✅ Kosti pre oči vytvorené")

def setup_shape_keys(mesh_obj):
    """Vytvorenie shape keys pre lip sync"""
    print("🔧 Vytváram shape keys...")
    
    bpy.context.view_layer.objects.active = mesh_obj
    
    # Basis shape key
    bpy.ops.object.shape_key_add(from_mix=False)
    mesh_obj.data.shape_keys.key_blocks[0].name = "Basis"
    
    # Viseme shape keys
    visemes = [
        "A", "E", "I", "O", "U", "M", "F", "S", "T", "L", "R",
        "Smile", "Frown", "Surprised", "Angry", "Blink_L", "Blink_R"
    ]
    
    for viseme in visemes:
        bpy.ops.object.shape_key_add(from_mix=False)
        shape_key = mesh_obj.data.shape_keys.key_blocks[-1]
        shape_key.name = f"Viseme_{viseme}"
        shape_key.value = 0.0
    
    print("✅ Shape keys vytvorené")

def create_lip_sync_controls():
    """Vytvorenie custom properties pre lip sync ovládanie"""
    print("🎚️ Vytváram lip sync controls...")
    
    # Vytvorenie empty objektu pre controls
    bpy.ops.object.empty_add(type='PLAIN_AXES', location=(3, 0, 2))
    controller = bpy.context.object
    controller.name = "LipSync_Controller"
    
    # Pridanie custom properties
    visemes = ["A", "E", "I", "O", "U", "M", "F", "S", "T", "L", "R"]
    
    for viseme in visemes:
        controller[f"Viseme_{viseme}"] = 0.0
        
        # Nastavenie min/max hodnôt
        controller_rna = controller.id_properties_ui(f"Viseme_{viseme}")
        controller_rna.update(min=0.0, max=1.0, soft_min=0.0, soft_max=1.0)
    
    # Pridanie master controls
    controller["Mouth_Open"] = 0.0
    controller["Jaw_Open"] = 0.0
    controller["Smile"] = 0.0
    
    for prop in ["Mouth_Open", "Jaw_Open", "Smile"]:
        controller_rna = controller.id_properties_ui(prop)
        controller_rna.update(min=0.0, max=1.0, soft_min=0.0, soft_max=1.0)
    
    print("✅ Lip sync controls vytvorené")
    return controller

def setup_audio_import():
    """Príprava na import audio súboru"""
    print("🎵 Pripravujem audio import...")
    
    # Nastavenie frame rate
    bpy.context.scene.render.fps = 24
    bpy.context.scene.frame_start = 1
    bpy.context.scene.frame_end = 250
    
    # Vytvorenie markera pre audio sync
    bpy.context.scene.timeline_markers.new("Audio_Start", frame=1)
    
    print("✅ Audio import pripravený")

def create_complete_rig():
    """Hlavná funkcia - vytvorenie kompletného rigu"""
    print("🎭 BLENDER LIP SYNC RIG - AUTOMATICKÉ VYTVORENIE")
    print("=" * 50)
    
    try:
        # 1. Nastavenie scény
        setup_blender_scene()
        
        # 2. Import postavy
        character_plane = import_character_image()
        if not character_plane:
            print("❌ Chyba pri importe postavy")
            return False
        
        # 3. Vytvorenie 3D mesh
        head_mesh = create_face_mesh()
        
        # 4. Vytvorenie kostí
        armature = create_mouth_bones()
        create_eye_bones(armature)
        
        # 5. Shape keys
        setup_shape_keys(head_mesh)
        
        # 6. Controls
        controller = create_lip_sync_controls()
        
        # 7. Audio príprava
        setup_audio_import()
        
        # 8. Finálne nastavenia
        bpy.context.view_layer.objects.active = armature
        bpy.ops.object.select_all(action='DESELECT')
        armature.select_set(True)
        head_mesh.select_set(True)
        
        # Parent mesh to armature
        bpy.ops.object.parent_set(type='ARMATURE_AUTO')
        
        # Uloženie súboru
        blend_path = os.path.join(os.path.dirname(__file__), "character_lipsync_rig.blend")
        bpy.ops.wm.save_as_mainfile(filepath=blend_path)
        
        print("\n" + "=" * 50)
        print("🎉 ÚSPECH! Lip sync rig vytvorený!")
        print(f"💾 Súbor uložený: {blend_path}")
        print("\n📋 Čo je pripravené:")
        print("   • Face_Rig - Armatúra s kosťami pre lip sync")
        print("   • Head - 3D mesh s shape keys")
        print("   • LipSync_Controller - Ovládacie prvky")
        print("   • Character_Base - 2D referenčný obrázok")
        print("\n🎵 Pripravené na import MP3:")
        print("   • Sequencer > Add > Sound > Váš MP3 súbor")
        print("   • Použite Viseme_* properties na animáciu")
        print("\n🎬 Ďalšie kroky:")
        print("   1. Importujte MP3 súbor")
        print("   2. Analyzujte audio pre visemes")
        print("   3. Animujte lip sync pomocou keyframes")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba pri vytváraní rigu: {e}")
        import traceback
        traceback.print_exc()
        return False

def import_audio_file(audio_path):
    """Import MP3 súboru do Blender sequencera"""
    print(f"🎵 Importujem audio: {audio_path}")
    
    if not os.path.exists(audio_path):
        print(f"❌ Audio súbor neexistuje: {audio_path}")
        return False
    
    # Prepnutie na Video Sequence Editor
    if not bpy.context.scene.sequence_editor:
        bpy.context.scene.sequence_editor_create()
    
    # Import audio
    bpy.ops.sequencer.sound_strip_add(filepath=audio_path, frame_start=1)
    
    # Nastavenie dĺžky animácie podľa audio
    for strip in bpy.context.scene.sequence_editor.sequences:
        if strip.type == 'SOUND':
            bpy.context.scene.frame_end = strip.frame_final_end
            break
    
    print("✅ Audio importované")
    return True

def setup_auto_lipsync(audio_path):
    """Kompletné nastavenie automatického lip sync"""
    print("🤖 Nastavujem automatický lip sync...")
    
    # Import audio
    if not import_audio_file(audio_path):
        return False
    
    # Nájdenie controllera
    controller = bpy.data.objects.get("LipSync_Controller")
    if not controller:
        print("❌ LipSync_Controller nenájdený")
        return False
    
    print("✅ Automatický lip sync nastavený")
    print("📋 Teraz môžete manuálne animovať lip sync pomocí keyframes")
    return True

if __name__ == "__main__":
    create_complete_rig()
