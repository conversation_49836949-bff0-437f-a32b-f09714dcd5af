// Adobe Photoshop Script pre vytvorenie štrukt<PERSON><PERSON> vrstiev pre Character Animator
// Čarodej - Automatické vytvorenie vrstiev a skupín

// Hlavná funkcia
function createCharacterAnimatorStructure() {
    // Skontroluj či je otvorený dokument
    if (app.documents.length == 0) {
        alert("Prosím otvorte PSD súbor pred spustením scriptu.");
        return;
    }
    
    var doc = app.activeDocument;
    
    // Vytvor hlavnú skupinu
    var mainGroup = doc.layerSets.add();
    mainGroup.name = "Carodej";
    
    // Vytvor Head skupinu
    var headGroup = mainGroup.layerSets.add();
    headGroup.name = "Head";
    
    // Face skupina
    var faceGroup = headGroup.layerSets.add();
    faceGroup.name = "Face";
    
    // Eye_Left skupina
    var eyeLeftGroup = faceGroup.layerSets.add();
    eyeLeftGroup.name = "Eye_Left";
    createEyeLayers(eyeLeftGroup, "Left");
    
    // Eye_Right skupina
    var eyeRightGroup = faceGroup.layerSets.add();
    eyeRightGroup.name = "Eye_Right";
    createEyeLayers(eyeRightGroup, "Right");
    
    // Eyebrows skupina
    var eyebrowsGroup = faceGroup.layerSets.add();
    eyebrowsGroup.name = "Eyebrows";
    createEyebrowLayers(eyebrowsGroup);
    
    // Mouth skupina
    var mouthGroup = faceGroup.layerSets.add();
    mouthGroup.name = "Mouth";
    createMouthLayers(mouthGroup);
    
    // Head base layer
    var headBase = headGroup.artLayers.add();
    headBase.name = "Head_Base";
    
    // Hat skupina
    var hatGroup = headGroup.layerSets.add();
    hatGroup.name = "Hat";
    createHatLayers(hatGroup);
    
    // Beard skupina
    var beardGroup = headGroup.layerSets.add();
    beardGroup.name = "Beard";
    createBeardLayers(beardGroup);
    
    // Body skupina
    var bodyGroup = mainGroup.layerSets.add();
    bodyGroup.name = "Body";
    createBodyStructure(bodyGroup);
    
    // Breathing skupina
    var breathingGroup = mainGroup.layerSets.add();
    breathingGroup.name = "Breathing";
    createBreathingLayers(breathingGroup);
    
    // Vytvor trigger vrstvy
    createTriggerLayers(mainGroup);
    
    alert("Štruktúra vrstiev pre Character Animator bola úspešne vytvorená!");
}

// Funkcia pre vytvorenie eye vrstiev
function createEyeLayers(group, side) {
    var eyeStates = ["Open", "Closed", "Squint", "Surprised"];
    
    for (var i = 0; i < eyeStates.length; i++) {
        var layer = group.artLayers.add();
        layer.name = "Eye_" + side + "_" + eyeStates[i];
        if (i > 0) layer.visible = false; // Iba Open je viditeľný
    }
}

// Funkcia pre vytvorenie eyebrow vrstiev
function createEyebrowLayers(group) {
    var eyebrowStates = [
        "Eyebrow_Left_Neutral", "Eyebrow_Left_Raised", "Eyebrow_Left_Furrowed",
        "Eyebrow_Right_Neutral", "Eyebrow_Right_Raised", "Eyebrow_Right_Furrowed"
    ];
    
    for (var i = 0; i < eyebrowStates.length; i++) {
        var layer = group.artLayers.add();
        layer.name = eyebrowStates[i];
        if (i != 0 && i != 3) layer.visible = false; // Iba neutral sú viditeľné
    }
}

// Funkcia pre vytvorenie mouth vrstiev (lip sync)
function createMouthLayers(group) {
    var mouthShapes = [
        "Mouth_Neutral", "Mouth_Smile", "Mouth_Frown", "Mouth_Open",
        "Mouth_Ah", "Mouth_D", "Mouth_Ee", "Mouth_F", "Mouth_L",
        "Mouth_M", "Mouth_Oh", "Mouth_Ooh", "Mouth_R", "Mouth_S",
        "Mouth_Th", "Mouth_W"
    ];
    
    for (var i = 0; i < mouthShapes.length; i++) {
        var layer = group.artLayers.add();
        layer.name = mouthShapes[i];
        if (i > 0) layer.visible = false; // Iba Neutral je viditeľný
    }
}

// Funkcia pre vytvorenie hat vrstiev
function createHatLayers(group) {
    var hatParts = ["Hat_Base", "Hat_Feather", "Hat_Band"];
    
    for (var i = 0; i < hatParts.length; i++) {
        var layer = group.artLayers.add();
        layer.name = hatParts[i];
    }
}

// Funkcia pre vytvorenie beard vrstiev
function createBeardLayers(group) {
    var beardParts = ["Beard_Base", "Beard_Movement"];
    
    for (var i = 0; i < beardParts.length; i++) {
        var layer = group.artLayers.add();
        layer.name = beardParts[i];
    }
}

// Funkcia pre vytvorenie body štruktúry
function createBodyStructure(bodyGroup) {
    // Torso skupina
    var torsoGroup = bodyGroup.layerSets.add();
    torsoGroup.name = "Torso";
    
    var torsoParts = ["Chest", "Robe_Front", "Robe_Back"];
    for (var i = 0; i < torsoParts.length; i++) {
        var layer = torsoGroup.artLayers.add();
        layer.name = torsoParts[i];
    }
    
    // Arms skupina
    var armsGroup = bodyGroup.layerSets.add();
    armsGroup.name = "Arms";
    
    // Left arm
    var leftArmGroup = armsGroup.layerSets.add();
    leftArmGroup.name = "Arm_Left";
    createArmStructure(leftArmGroup, "Left");
    
    // Right arm
    var rightArmGroup = armsGroup.layerSets.add();
    rightArmGroup.name = "Arm_Right";
    createArmStructure(rightArmGroup, "Right");
    
    // Accessories skupina
    var accessoriesGroup = bodyGroup.layerSets.add();
    accessoriesGroup.name = "Accessories";
    createAccessories(accessoriesGroup);
}

// Funkcia pre vytvorenie arm štruktúry
function createArmStructure(armGroup, side) {
    var armParts = ["Shoulder_" + side, "Upper_Arm_" + side, "Lower_Arm_" + side];
    
    for (var i = 0; i < armParts.length; i++) {
        var layer = armGroup.artLayers.add();
        layer.name = armParts[i];
    }
    
    // Hand skupina
    var handGroup = armGroup.layerSets.add();
    handGroup.name = "Hand_" + side;
    
    var handGestures = ["Neutral", "Point", "Fist", "Open", "Peace", "Thumbs_Up"];
    for (var i = 0; i < handGestures.length; i++) {
        var layer = handGroup.artLayers.add();
        layer.name = "Hand_" + side + "_" + handGestures[i];
        if (i > 0) layer.visible = false; // Iba Neutral je viditeľný
    }
}

// Funkcia pre vytvorenie accessories
function createAccessories(group) {
    // Staff skupina
    var staffGroup = group.layerSets.add();
    staffGroup.name = "Staff";
    
    var staffParts = ["Staff_Base", "Staff_Crystal", "Staff_Glow"];
    for (var i = 0; i < staffParts.length; i++) {
        var layer = staffGroup.artLayers.add();
        layer.name = staffParts[i];
    }
    
    // Belt skupina
    var beltGroup = group.layerSets.add();
    beltGroup.name = "Belt";
    
    var beltParts = ["Belt_Base", "Belt_Pouch"];
    for (var i = 0; i < beltParts.length; i++) {
        var layer = beltGroup.artLayers.add();
        layer.name = beltParts[i];
    }
}

// Funkcia pre vytvorenie breathing vrstiev
function createBreathingLayers(group) {
    var breathingStates = ["Inhale", "Exhale"];
    
    for (var i = 0; i < breathingStates.length; i++) {
        var layer = group.artLayers.add();
        layer.name = breathingStates[i];
        if (i > 0) layer.visible = false; // Iba Inhale je viditeľný
    }
}

// Funkcia pre vytvorenie trigger vrstiev
function createTriggerLayers(mainGroup) {
    var triggers = [
        "+Happy", "+Sad", "+Surprised", "+Angry", "+Thinking",
        "+Wave", "+Point", "+Magic_Cast", "+Thumbs_Up",
        "+Spell_Cast", "+Laugh", "+Wink_Left", "+Wink_Right"
    ];
    
    for (var i = 0; i < triggers.length; i++) {
        var layer = mainGroup.artLayers.add();
        layer.name = triggers[i];
        layer.visible = false; // Trigger vrstvy sú neviditeľné
    }
}

// Spusti hlavnú funkciu
createCharacterAnimatorStructure();
